import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { API_URL } from '../config';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faTimes, faSync } from '@fortawesome/free-solid-svg-icons';

const RankCandidatesModal = ({ isOpen, onClose, jobId, jobTitle, candidateIds, candidates, darkMode = false }) => {
  const [rankings, setRankings] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [isReranking, setIsReranking] = useState(false);
  
  useEffect(() => {
    // Check if both jobId and candidates are available
    if (isOpen && jobId && candidateIds && candidateIds.length > 0) {
      fetchRankings();
    }
  }, [isOpen, jobId, candidateIds]);
  
  const fetchRankings = async (forceRefresh = false) => {
    if (!jobId) return;
    
    setLoading(true);
    setError('');
    
    try {
      const token = localStorage.getItem('token');
      
      if (forceRefresh) {
        // Use POST to force a refresh of the rankings
        setIsReranking(true);
        
        // Call the ranking API
        const response = await axios.post(
          `${API_URL}/api/rank-candidates/`, 
          {
            job_id: jobId,
            candidate_ids: candidateIds,
            force_refresh: true
          },
          {
            headers: {
              'Authorization': `Token ${token}`,
              'Content-Type': 'application/json'
            }
          }
        );
        
        setRankings(response.data);
        setIsReranking(false);
      } else {
        // Try to get existing rankings first
        try {
          const response = await axios.get(
            `${API_URL}/api/rank-candidates/?job_id=${jobId}`,
            {
              headers: {
                'Authorization': `Token ${token}`,
                'Content-Type': 'application/json'
              }
            }
          );
          
          if (response.data && response.data.length > 0) {
            console.log('Found existing rankings:', response.data);
            setRankings(response.data);
          } else {
            // If no existing rankings, generate new ones
            generateNewRankings();
          }
        } catch (err) {
          // If error (like 404 no rankings found), generate new rankings
          console.log('No existing rankings found, generating new ones');
          generateNewRankings();
        }
      }
    } catch (error) {
      console.error('Error fetching rankings:', error);
      setError('Failed to rank candidates. Please try again.');
    } finally {
      setLoading(false);
    }
  };
  
  const generateNewRankings = async () => {
    try {
      const token = localStorage.getItem('token');
      
      // Call the ranking API
      const response = await axios.post(
        `${API_URL}/api/rank-candidates/`, 
        {
          job_id: jobId,
          candidate_ids: candidateIds
        },
        {
          headers: {
            'Authorization': `Token ${token}`,
            'Content-Type': 'application/json'
          }
        }
      );
      
      setRankings(response.data);
    } catch (error) {
      console.error('Error generating rankings:', error);
      setError('Failed to rank candidates. Please try again.');
    }
  };
  
  const handleRerank = () => {
    fetchRankings(true);
  };
  
  if (!isOpen) return null;
  
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className={`${darkMode ? 'bg-gray-800 text-white' : 'bg-white'} rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-hidden`}>
        <div className="flex justify-between items-center bg-blue-600 text-white p-4">
          <h2 className="text-xl font-semibold">
            Candidate Rankings for {jobTitle}
          </h2>
          <button 
            onClick={onClose}
            className="text-white hover:text-gray-200"
          >
            <FontAwesomeIcon icon={faTimes} />
          </button>
        </div>
        
        <div className="p-4 overflow-y-auto max-h-[calc(90vh-8rem)]">
          {loading && !isReranking ? (
            <div className="text-center py-10">
              <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p className={`${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>Loading rankings...</p>
            </div>
          ) : error ? (
            <div className={`${darkMode ? 'bg-red-900 text-red-200' : 'bg-red-100 text-red-700'} p-4 rounded-md mb-4`}>
              {error}
            </div>
          ) : (
            <>
              <div className="flex justify-between items-center mb-4">
                <h3 className={`text-lg font-medium ${darkMode ? 'text-gray-200' : 'text-gray-800'}`}>
                  Ranked Candidates ({rankings.length})
                </h3>
                <button
                  onClick={handleRerank}
                  className={`px-4 py-2 flex items-center space-x-2 ${isReranking ? 'opacity-50 cursor-not-allowed' : ''} bg-blue-600 text-white rounded hover:bg-blue-700`}
                  disabled={isReranking}
                >
                  {isReranking ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                      <span>Reranking...</span>
                    </>
                  ) : (
                    <>
                      <FontAwesomeIcon icon={faSync} />
                      <span>Rerank</span>
                    </>
                  )}
                </button>
              </div>
              
              {rankings.length > 0 ? (
                <div className="space-y-4">
                  {rankings.map((ranking, index) => {
                    // Find the candidate by ID
                    const candidate = candidates.find(c => c.id === ranking.candidate_id) || {};
                    
                    return (
                      <div 
                        key={ranking.id || index}
                        className={`border rounded-lg overflow-hidden ${darkMode ? 'border-gray-600' : 'border-gray-200'}`}
                      >
                        <div className={`flex justify-between items-center p-3 ${darkMode ? 'bg-gray-700' : 'bg-white'}`}>
                          <div className="flex-1">
                            <div className="flex items-center">
                              <div className={`flex-shrink-0 w-8 h-8 rounded-full ${darkMode ? 'bg-blue-900 text-blue-200' : 'bg-blue-100 text-blue-800'} flex items-center justify-center font-bold mr-3`}>
                                {index + 1}
                              </div>
                              <div>
                                <h4 className="font-semibold">{candidate.name || 'Unnamed Candidate'}</h4>
                                <div className="text-sm flex space-x-4">
                                  <span className={`${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                                    {candidate.preferred_role_display || 'Unknown Role'}
                                  </span>
                                  {candidate.total_experience && (
                                    <span className={`${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                                      {candidate.total_experience} years exp.
                                    </span>
                                  )}
                                </div>
                              </div>
                            </div>
                          </div>
                          <div className={`px-3 py-1 rounded ${
                            (ranking.match_score || ranking.score) > 80 
                              ? darkMode ? 'bg-green-900 text-green-200' : 'bg-green-100 text-green-800' 
                              : (ranking.match_score || ranking.score) > 60 
                                ? darkMode ? 'bg-yellow-900 text-yellow-200' : 'bg-yellow-100 text-yellow-800'
                                : darkMode ? 'bg-red-900 text-red-200' : 'bg-red-100 text-red-800'
                          }`}>
                            <div className="font-bold">
                              {/* Check for different score property names and use a fallback if not found */}
                              {ranking.match_score !== undefined 
                                ? `${ranking.match_score}%` 
                                : ranking.score !== undefined 
                                  ? `${ranking.score}%`
                                  : ranking.match_percentage !== undefined
                                    ? `${ranking.match_percentage}%`
                                    : '0%'}
                            </div>
                            <div className="text-xs">Match Score</div>
                          </div>
                        </div>
                        <div className={`p-3 ${darkMode ? 'bg-gray-600 text-gray-200' : 'bg-gray-50 text-gray-800'} text-sm`}>
                          <h5 className="font-semibold mb-1">AI Analysis:</h5>
                          <p>{ranking.reasoning}</p>
                          
                          {/* Skills Comparison Section */}
                          <div className="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div className={`border rounded p-2 ${darkMode ? 'bg-green-900 border-green-800' : 'bg-green-50 border-green-100'}`}>
                              <h6 className={`font-semibold ${darkMode ? 'text-green-200' : 'text-green-700'} mb-2`}>Matching Skills</h6>
                              {ranking.matching_skills && ranking.matching_skills.length > 0 ? (
                                <div className="flex flex-wrap gap-2">
                                  {ranking.matching_skills.map((skill, index) => (
                                    <span 
                                      key={index} 
                                      className={`px-2 py-1 rounded-full text-xs ${darkMode ? 'bg-green-800 text-green-100' : 'bg-green-100 text-green-800'}`}
                                    >
                                      {skill}
                                    </span>
                                  ))}
                                </div>
                              ) : (
                                <p className={`${darkMode ? 'text-gray-400' : 'text-gray-500'} text-xs`}>
                                  {ranking.is_cached ? 
                                    "Skill matching is only available for newly ranked or re-ranked candidates" : 
                                    "No matching skills identified"}
                                </p>
                              )}
                            </div>
                            
                            <div className={`border rounded p-2 ${darkMode ? 'bg-red-900 border-red-800' : 'bg-red-50 border-red-100'}`}>
                              <h6 className={`font-semibold ${darkMode ? 'text-red-200' : 'text-red-700'} mb-2`}>Missing Skills</h6>
                              {ranking.missing_skills && ranking.missing_skills.length > 0 ? (
                                <div className="flex flex-wrap gap-2">
                                  {ranking.missing_skills.map((skill, index) => (
                                    <span 
                                      key={index} 
                                      className={`px-2 py-1 rounded-full text-xs ${darkMode ? 'bg-red-800 text-red-100' : 'bg-red-100 text-red-800'}`}
                                    >
                                      {skill}
                                    </span>
                                  ))}
                                </div>
                              ) : (
                                <p className={`${darkMode ? 'text-gray-400' : 'text-gray-500'} text-xs`}>
                                  {ranking.is_cached ? 
                                    "Skill gap analysis is only available for newly ranked or re-ranked candidates" : 
                                    "No missing skills identified"}
                                </p>
                              )}
                            </div>
                          </div>
                          
                          {/* Skills note for cached results */}
                          {ranking.skills_note && ranking.is_cached && (
                            <div className={`mt-2 text-xs ${darkMode ? 'text-gray-400' : 'text-gray-500'} italic`}>
                              Note: {ranking.skills_note}
                            </div>
                          )}
                        </div>
                      </div>
                    );
                  })}
                </div>
              ) : (
                <p className={`text-center py-4 ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>No ranking results available.</p>
              )}
            </>
          )}
        </div>
        
        <div className={`border-t p-4 ${darkMode ? 'bg-gray-700 border-gray-600' : 'bg-gray-50 border-gray-200'}`}>
          <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
            <p>Candidates are ranked based on how well their resume and profile match the job requirements.</p>
            <p>Rankings are stored and updated each time you perform a re-rank.</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RankCandidatesModal;
