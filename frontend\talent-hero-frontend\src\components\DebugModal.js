import React from 'react';

const DebugModal = ({ isOpen, onClose, debugInfo, darkMode = false }) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className={`${darkMode ? 'bg-gray-800 text-white' : 'bg-white'} p-6 rounded-lg w-full max-w-2xl mx-auto overflow-y-auto max-h-[90vh]`}>
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold">Debug Information</h2>
          <button
            onClick={onClose}
            className={`${darkMode ? 'text-gray-400 hover:text-gray-200' : 'text-gray-500 hover:text-gray-700'}`}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
        
        <div className={`p-4 rounded-md ${darkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
          <div className="space-y-3">
            <div>
              <strong>Loading State:</strong> {debugInfo.isLoading ? 'Yes' : 'No'}
            </div>
            <div>
              <strong>Error:</strong> {debugInfo.error || 'None'}
            </div>
            <div>
              <strong>Data Type:</strong> {Array.isArray(debugInfo.data) ? 'Array' : typeof debugInfo.data}
            </div>
            <div>
              <strong>Data Length:</strong> {Array.isArray(debugInfo.data) ? debugInfo.data.length : 'N/A'}
            </div>
            {Array.isArray(debugInfo.data) && debugInfo.data.length > 0 && (
              <div>
                <strong>First Item Keys:</strong>
                <div className={`mt-2 p-2 rounded ${darkMode ? 'bg-gray-600' : 'bg-gray-200'}`}>
                  <code className="text-sm">
                    {Object.keys(debugInfo.data[0]).join(', ')}
                  </code>
                </div>
              </div>
            )}
            {Array.isArray(debugInfo.data) && debugInfo.data.length > 0 && (
              <div>
                <strong>First Item Data:</strong>
                <div className={`mt-2 p-2 rounded ${darkMode ? 'bg-gray-600' : 'bg-gray-200'} max-h-60 overflow-y-auto`}>
                  <pre className="text-xs">
                    {JSON.stringify(debugInfo.data[0], null, 2)}
                  </pre>
                </div>
              </div>
            )}
            <div>
              <strong>Last Fetch Time:</strong> {debugInfo.lastFetchTime || 'Never'}
            </div>
          </div>
        </div>
        
        <div className="mt-6 flex justify-end">
          <button
            onClick={onClose}
            className={`px-4 py-2 ${darkMode ? 'bg-gray-700 text-gray-200 hover:bg-gray-600' : 'bg-gray-200 text-gray-800 hover:bg-gray-300'} rounded`}
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

export default DebugModal;
