import React from 'react';

const InterviewLevel = ({ 
  level, 
  levelData, 
  onChange, 
  onDelete, 
  canDelete = true, 
  darkMode = false 
}) => {
  const handleChange = (e) => {
    const { name, value } = e.target;
    onChange(level, name, value);
  };

  return (
    <div className={`p-4 rounded-lg border ${darkMode ? 'bg-gray-700 border-gray-600' : 'bg-gray-50 border-gray-200'} mb-4`}>
      <div className="flex justify-between items-center mb-4">
        <h3 className={`text-lg font-medium ${darkMode ? 'text-white' : 'text-gray-800'}`}>
          {level} Interview Details
        </h3>
        {canDelete && (
          <button
            type="button"
            onClick={() => onDelete(level)}
            className="text-red-600 hover:text-red-800 p-1"
            title={`Delete ${level} Level`}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
            </svg>
          </button>
        )}
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Status */}
        <div>
          <label className={`block text-sm font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'} mb-1`}>
            Status
          </label>
          <select
            name="status"
            value={levelData.status || ''}
            onChange={handleChange}
            className={`w-full p-2 border ${darkMode ? 'bg-gray-600 border-gray-500 text-white' : 'border-gray-300 text-gray-800'} rounded focus:outline-none focus:ring-2 focus:ring-blue-500`}
          >
            <option value="">Select Status</option>
            <option value="scheduled">Scheduled</option>
            <option value="attended">Attended</option>
            <option value="hold">Hold</option>
            <option value="rejected">Rejected</option>
            <option value="selected">Selected</option>
            <option value="dropoff">DropOff</option>
          </select>
        </div>

        {/* Schedule Date */}
        <div>
          <label className={`block text-sm font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'} mb-1`}>
            Schedule Date
          </label>
          <input
            type="date"
            name="schedule_date"
            value={levelData.schedule_date || ''}
            onChange={handleChange}
            className={`w-full p-2 border ${darkMode ? 'bg-gray-600 border-gray-500 text-white' : 'border-gray-300 text-gray-800'} rounded focus:outline-none focus:ring-2 focus:ring-blue-500`}
          />
        </div>

        {/* Schedule Time */}
        <div>
          <label className={`block text-sm font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'} mb-1`}>
            Schedule Time
          </label>
          <input
            type="time"
            name="schedule_time"
            value={levelData.schedule_time || ''}
            onChange={handleChange}
            className={`w-full p-2 border ${darkMode ? 'bg-gray-600 border-gray-500 text-white' : 'border-gray-300 text-gray-800'} rounded focus:outline-none focus:ring-2 focus:ring-blue-500`}
          />
        </div>

        {/* Panel Name */}
        <div>
          <label className={`block text-sm font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'} mb-1`}>
            Panel Name
          </label>
          <input
            type="text"
            name="panel_name"
            value={levelData.panel_name || ''}
            onChange={handleChange}
            className={`w-full p-2 border ${darkMode ? 'bg-gray-600 border-gray-500 text-white placeholder-gray-400' : 'border-gray-300 text-gray-800'} rounded focus:outline-none focus:ring-2 focus:ring-blue-500`}
            placeholder="Enter panel member name"
          />
        </div>
      </div>

      {/* Panel Comment / Score */}
      <div className="mt-4">
        <label className={`block text-sm font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'} mb-1`}>
          Panel Comment / Score
        </label>
        <textarea
          name="panel_comment"
          value={levelData.panel_comment || ''}
          onChange={handleChange}
          className={`w-full p-2 border ${darkMode ? 'bg-gray-600 border-gray-500 text-white placeholder-gray-400' : 'border-gray-300 text-gray-800'} rounded focus:outline-none focus:ring-2 focus:ring-blue-500`}
          placeholder="Enter panel comments and score"
          rows="3"
        ></textarea>
      </div>
    </div>
  );
};

export default InterviewLevel;
