# Talent Hero

A modern authentication and user management application with a React/Tailwind frontend and Django/PostgreSQL backend.

## Project Structure

```
TH-v3/
├── backend/                  # Django REST API backend
│   ├── authentication/       # Custom authentication app
│   ├── jobs/                 # Jobs management app
│   ├── candidates/           # Candidates management app
│   │   ├── models.py         # Candidate and CandidateRanking models
│   │   ├── views.py          # API endpoints including ranking
│   │   ├── ranking.py        # AI-powered candidate ranking
│   │   └── urls.py           # API URL routing
│   ├── talent_hero/          # Main Django project
│   └── requirements.txt      # Python dependencies
└── frontend/                 # React frontend
    └── talent-hero-frontend/ # React application with Tailwind CSS
        ├── src/
        │   ├── components/
        │   │   ├── Dashboard.js           # Main application dashboard
        │   │   ├── JobCard.js             # Job listing component
        │   │   ├── CandidateCard.js       # Candidate listing component
        │   │   ├── SavedFiltersView.js    # Saved filters management
        │   │   ├── RankCandidatesModal.js # AI-powered ranking interface
        │   │   └── ...                    # Other UI components
        │   ├── contexts/
        │   │   └── AuthContext.js         # Authentication context
        │   └── config.js                  # Application configuration
        └── package.json                   # Frontend dependencies
```

## Prerequisites

- Python 3.x
- Node.js and npm
- PostgreSQL database server

## Complete Setup Guide

### 1. Database Setup

1. Install PostgreSQL if you haven't already
   
2. Create a PostgreSQL database:
   ```sql
   CREATE DATABASE "talentHero";
   ```
   
3. Configure PostgreSQL with:
   - Username: postgres
   - Password: postgres123
   - Port: 5432

### 2. Backend Setup

1. Navigate to the backend directory:
   ```
   cd backend
   ```

2. Install the required dependencies:
   ```
   pip install -r requirements.txt
   ```

3. Create and apply database migrations:
   ```
   python manage.py makemigrations
   python manage.py migrate
   ```

4. Create a superuser (this will be your admin account):
   ```
   python manage.py createsuperuser
   ```
   - You'll be prompted to enter an email, username, and password
   - Make sure to remember these credentials for admin access

5. Start the Django development server:
   ```
   python manage.py runserver
   ```
   - The server will be available at `http://localhost:8000`
   - You should see a JSON response at the root URL with API endpoints

### 3. Frontend Setup

1. Navigate to the frontend directory:
   ```
   cd frontend/talent-hero-frontend
   ```

2. Install the required dependencies:
   ```
   npm install
   ```

3. Start the React development server:
   ```
   npm start
   ```
   - The application will be available at `http://localhost:3000`

## Server Deployment Instructions

### Prerequisites

1. A server with Python 3.10+ and Node.js 16+ installed
2. PostgreSQL database
3. Ollama installed for AI-powered features

### Backend Deployment Steps

1. Clone the repository on your server:
   ```bash
   git clone https://your-repository-url/TH-v3.git
   cd TH-v3
   ```

2. Set up a Python virtual environment:
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. Install backend dependencies:
   ```bash
   cd backend
   pip install -r requirements.txt
   ```

4. Configure your database in `backend/talent_hero/settings.py`:
   ```python
   DATABASES = {
       'default': {
           'ENGINE': 'django.db.backends.postgresql',
           'NAME': 'your_db_name',
           'USER': 'your_db_user',
           'PASSWORD': 'your_db_password',
           'HOST': 'localhost',  # Or your DB host
           'PORT': '5432',
       }
   }
   ```

5. Run migrations:
   ```bash
   python manage.py migrate
   ```

6. Create a superuser:
   ```bash
   python manage.py createsuperuser
   ```

7. Set up Ollama for candidate ranking:
   ```bash
   # Install Ollama following instructions at ollama.ai
   ollama pull cogito:3b  # or a smaller model like tinyllama
   then run ollama serve
   ```

8. Start the Django server:
   ```bash
   # For development
   python manage.py runserver
   
   # For production (using Gunicorn)
   pip install gunicorn
   gunicorn talent_hero.wsgi:application --bind 0.0.0.0:8000
   ```

### Frontend Deployment Steps

1. Install frontend dependencies:
   ```bash
   cd frontend/talent-hero-frontend
   npm install
   ```

2. Update the API URL in `src/config.js` to point to your server:
   ```javascript
   export const API_URL = 'http://your-server-domain:8000';
   ```

3. Build the frontend for production:
   ```bash
   npm run build
   ```

4. Serve the built frontend using Nginx or a similar web server:
   ```
   # Example Nginx configuration
   server {
       listen 80;
       server_name your-domain.com;
       
       location / {
           root /path/to/TH-v3/frontend/talent-hero-frontend/build;
           try_files $uri $uri/ /index.html;
       }
       
       location /api {
           proxy_pass http://localhost:8000;
           proxy_set_header Host $host;
           proxy_set_header X-Real-IP $remote_addr;
       }
   }
   ```

## Environment-Specific Guidelines

### Development Environment

1. **Configuration**:
   - In `backend/talent_hero/settings.py`, ensure `DEBUG = True`
   - Use a local PostgreSQL database for development
   - Configure CORS to allow connections from your frontend development server:
     ```python
     CORS_ALLOWED_ORIGINS = [
         "http://localhost:3000",
     ]
     ```

2. **Running the Services**:
   - Backend: `python manage.py runserver` (runs on port 8000)
   - Frontend: `npm start` (runs on port 3000)
   - Ollama: `ollama serve` (runs on port 11434)

3. **Development Workflow**:
   - Make changes in the code
   - Test locally through the React UI or API testing tools like Postman
   - Use Django shell for backend debugging: `python manage.py shell`
   - Check Django logs in the terminal where `runserver` is running
   - Monitor React logs in the browser console and terminal

4. **Working with Models**:
   - After changing models, create migrations: `python manage.py makemigrations`
   - Apply migrations: `python manage.py migrate`
   - Test migrations locally before pushing to production

### Testing Environment

1. **Configuration**:
   - Create a separate database for testing
   - In your CI/CD pipeline, use a testing-specific settings file
   - Consider using SQLite for faster test execution:
     ```python
     DATABASES = {
         'default': {
             'ENGINE': 'django.db.backends.sqlite3',
             'NAME': BASE_DIR / 'test_db.sqlite3',
         }
     }
     ```

2. **Running Tests**:
   - Backend tests: `python manage.py test`
   - Frontend tests: `npm test`
   - Test coverage: `coverage run --source='.' manage.py test` (requires coverage package)

3. **Mocking External Services**:
   - For testing without Ollama, modify `candidates/ranking.py` to use mock responses
   - Use environment variables to control whether to use real or mock services
   - Example mock for Ollama:
     ```python
     # In ranking.py
     if os.getenv('MOCK_OLLAMA') == 'true':
         return {
             "score": 7,
             "reasoning": "This is a mock AI assessment for testing purposes."
         }
     ```

### Production Environment

1. **Configuration**:
   - In `backend/talent_hero/settings.py`:
     - Set `DEBUG = False`
     - Configure a strong `SECRET_KEY`
     - Set up proper `ALLOWED_HOSTS`
     - Configure production database
   - Ensure all sensitive information is in environment variables, not in code
   - Example production settings:
     ```python
     DEBUG = False
     SECRET_KEY = os.environ.get('DJANGO_SECRET_KEY')
     ALLOWED_HOSTS = ['talent-hero.yourdomain.com']
     CORS_ALLOWED_ORIGINS = [
         "https://talent-hero.yourdomain.com",
     ]
     ```

2. **Deployment**:
   - Backend:
     - Use Gunicorn or uWSGI as the WSGI server
     - Set up Nginx as a reverse proxy
     - Configure proper SSL certificates
   - Frontend:
     - Build with `npm run build`
     - Serve static files with Nginx
   - Database:
     - Use a managed PostgreSQL service or properly configured self-hosted instance
     - Set up regular backups
   - Ollama:
     - Run on a machine with sufficient RAM and CPU (at least 8GB RAM recommended)
     - Consider setting up as a separate service to isolate resource usage

3. **Security Considerations**:
   - Implement rate limiting on API endpoints
   - Set up proper firewall rules
   - Configure proper SSL/TLS
   - Monitor application logs for suspicious activity
   - Regularly update dependencies

4. **Monitoring and Maintenance**:
   - Set up application monitoring (e.g., Prometheus, Grafana)
   - Implement error tracking (e.g., Sentry)
   - Configure log aggregation for troubleshooting
   - Set up regular database backups
   - Establish a regular update schedule for dependencies

5. **Scaling Considerations**:
   - For the AI ranking feature, consider:
     - Running Ollama on a separate machine with adequate resources
     - Implementing a queue system for processing ranking requests
     - Caching results to reduce load on the AI service
     - Using smaller models in production if performance is an issue

## Known Issues and Troubleshooting

1. **Ollama Timeouts**: If candidate ranking is slow or timing out:
   - Try using a smaller model (e.g., tinyllama instead of cogito:3b)
   - Increase system resources (RAM, CPU) for Ollama
   - Update `OLLAMA_MODEL` in `backend/candidates/ranking.py`
   - Adjust the timeout setting in `backend/candidates/ranking.py` if needed

2. **Database Migrations**: If you encounter issues with missing fields:
   - Make sure all migrations have been applied: `python manage.py migrate`
   - Check for any pending migrations: `python manage.py showmigrations`

3. **Frontend API Connection**: If the frontend can't connect to the backend:
   - Verify that the API_URL in config.js is correct
   - Check CORS settings in Django settings.py
   - Ensure your server firewall allows connections to the relevant ports

## Recent Updates

### Transcript Evaluation System (April 2025)

- **Enhanced AI-Powered Interview Transcript Evaluation**
  - Implemented text chunking for handling large transcripts beyond model context limits
  - Added robust error handling with intelligent JSON parsing for AI model responses
  - Improved file download system with proper browser content handling
  - Added technical_strengths field to provide more comprehensive candidate assessment
  - Structured evaluation reports with clear sections for different assessment criteria
  - Created fallback mechanisms for AI processing errors

### AI-Powered Candidate Ranking

- Implemented persistent ranking storage in the database
- Added ability to re-rank candidates against job descriptions
- Improved error handling for Ollama connection issues
- Added detailed candidate profiles for better AI evaluation
- Now supporting cached ranking results for faster retrieval

## Features

### Authentication System

- Uses custom User model with email as the primary identifier
- Token-based authentication with Django REST framework
- Admin-only endpoints for user management
- Automatic admin recognition for users with is_admin, is_staff, or is_superuser flags
- Protected routes for authenticated users using React Router
- Authentication state managed through Context API

### Dashboard with Sidebar Navigation

- Modern sidebar with interactive navigation options:
  - Dashboard (main overview)
  - Jobs (job management)
  - Candidates (candidate profiles - placeholder)
  - Status (recruitment status - placeholder)
- Dynamic content area that changes based on selected section
- Mobile-responsive design

### Job Management

- Complete CRUD functionality for job listings:
  - **Create**: Add new jobs with role selection from predefined options, description document, etc.
  - **Read**: View jobs as clean, informative cards
  - **Update**: Edit existing jobs through a modal
  - **Delete**: Remove jobs with confirmation dialog
- File upload for job description documents
- Status indicators for recruitment progress (Active, On Hold, Stopped)
- Job details include:
  - Job Title (dropdown with predefined roles: DevOps Engineer, Data Analyst, QA Testing Engineer, etc.)
  - Job Description Document (file upload)
  - Positions Available
  - Hiring Team
  - Hiring Manager
  - TA Incharge
  - Position Created On
  - Recruitment Status

### Candidate Management

- Complete CRUD functionality for candidate profiles:
  - **Create**: Add new candidates with role selection, resume upload, experience details, etc.
  - **Read**: View candidates as clean, informative cards
  - **Update**: Edit existing candidates through a modal
  - **Delete**: Remove candidates with confirmation dialog
- Resume upload and download functionality
- Visual indicators for hiring status
- Candidate details include:
  - Candidate Name
  - Target/Preferred Role (dropdown selection)
  - Optional Roles
  - Resume (file upload)
  - Total Experience
  - Last Date of Job
  - Hiring Status (detailed recruitment pipeline tracking)
  - Comments (for notes and feedback)

### Candidate Filtering System

- Filter candidates by job role and hiring status
- Filter button on each job card that opens a filtering modal
- Multi-select hiring status options (L1/L2/L3 stages)
- Save filters with a name for future reference
- Access saved filters via the "View Saved Filters" button on the Jobs page
- Refilter to update with newly added candidates

### AI-Powered Candidate Ranking

- Automatically rank candidates against job descriptions using AI
- Score candidates on a scale of 1-10 based on resume-job match
- Provide detailed reasoning for each candidate's score
- Sort candidates by match score to prioritize the best fits
- Works with candidate resumes and job description documents
- Built-in integration with Ollama's cogito:3b model for local inference
- Accessible directly from saved candidate filters

### Admin Features

- User Management:
  - Create new users with optional admin privileges
  - View list of all users in the system
  - Role-based access control
- Access to Django admin panel for advanced management

## Database Schema

The application uses PostgreSQL as its database system with the following tables:

### Authentication Tables

1. **User Model** (`authentication_user`)
   - Primary Key: `id` (Auto-incrementing integer)
   - `email` - Email address (unique identifier for login)
   - `username` - Username (for display purposes)
   - `password` - Hashed password
   - `is_admin` - Boolean flag for admin privileges
   - Standard Django user fields: `is_active`, `is_staff`, `is_superuser`, etc.

2. **Auth Token** (`authtoken_token`)
   - `key` - Unique token string
   - `user_id` - Foreign key to User model
   - `created` - Timestamp when token was created

### Job Management Tables

1. **Job Model** (`jobs_job`)
   - Primary Key: `id` (Auto-incrementing integer)
   - `title` - Job title (CharField with choices, e.g., 'devops', 'data_analyst', etc.)
   - `description_document` - File path to uploaded document (FileField, optional)
   - `positions_available` - Number of openings (PositiveIntegerField, optional)
   - `hiring_team` - Team responsible for hiring (CharField, optional)
   - `hiring_manager` - Person managing the hiring process (CharField, optional)
   - `ta_incharge` - Talent acquisition person responsible (CharField, optional)
   - `created_on` - Date when position was created (DateField, optional)
   - `recruitment_status` - Current status (CharField with choices: 'active', 'hold', 'stopped')
   - `created_by` - Foreign key to User model (creator of job listing)
   - `created_at` - Timestamp when job was created in the system
   - `updated_at` - Timestamp when job was last updated

### Candidate Management Tables

1. **Candidate Model** (`candidates_candidate`)
   - Primary Key: `id` (Auto-incrementing integer)
   - `name` - Candidate's name (CharField, optional)
   - `preferred_role` - Target role (CharField with choices, optional)
   - `optional_roles` - Other roles the candidate may be suitable for (CharField, optional)
   - `resume` - File path to uploaded resume (FileField, optional)
   - `total_experience` - Years of experience (DecimalField, optional)
   - `last_job_date` - Date of last employment (DateField, optional)
   - `hiring_status` - Current stage in the hiring process (CharField with choices)
   - `comments` - Notes and feedback about the candidate (TextField, optional)
   - `created_by` - Foreign key to User model (creator of candidate profile)
   - `created_at` - Timestamp when candidate was created in the system
   - `updated_at` - Timestamp when candidate was last updated

## File Storage

### Structure

```
TH-v3/
├── backend/
│   ├── media/                 # All uploaded files
│   │   └── job_descriptions/  # Job description documents
│   │   └── resumes/           # Candidate resumes
```

### How File Storage Works

1. **File Upload Path Configuration**:
   - Files are stored in the `media/job_descriptions/` and `media/resumes/` directories
   - The path is configured in `settings.py` with `MEDIA_ROOT` and `MEDIA_URL`
   - A special directory constant `JOB_DESCRIPTION_DIR` and `RESUME_DIR` ensures files go to the right location

2. **Database Storage**:
   - The database stores the relative path to the file (e.g., 'job_descriptions/filename.pdf' or 'resumes/filename.pdf')
   - The full URL is generated by the API using the `request.build_absolute_uri()` method

3. **File Access**:
   - In development, Django serves media files directly (configured in urls.py)
   - The API returns both the file URL and filename for frontend use

4. **Document Download**:
   - Job cards display a download icon if a document is attached
   - Clicking the icon opens the document in a new tab or downloads it directly
   - The tooltip shows "JD Download" for clarity
   - Candidate cards display a download icon if a resume is attached
   - Clicking the icon opens the resume in a new tab or downloads it directly
   - The tooltip shows "Resume Download" for clarity

### Job Description Document Workflow

1. **Upload**:
   - User attaches a document in the Add/Edit Job modal
   - Document is sent to the backend as FormData
   - Django saves the file to the `media/job_descriptions/` directory

2. **Storage**:
   - The file path is stored in the `description_document` field of the Job model
   - Additional properties are available:
     - `description_document_url`: Full URL to the document
     - `description_document_filename`: Just the filename portion

3. **Retrieval**:
   - When viewing job listings, the frontend receives the full document URL
   - The JobCard component displays the filename and provides a download button

4. **Downloading**:
   - Clicking the download button opens the document URL in a new tab
   - Browser handles the file download based on content type
   - No embedded document viewer is used, keeping the interface clean and simple

### Resume Upload Workflow

1. **Upload**:
   - User attaches a resume in the Add/Edit Candidate modal
   - Resume is sent to the backend as FormData
   - Django saves the file to the `media/resumes/` directory

2. **Storage**:
   - The file path is stored in the `resume` field of the Candidate model
   - Additional properties are available:
     - `resume_url`: Full URL to the resume
     - `resume_filename`: Just the filename portion

3. **Retrieval**:
   - When viewing candidate profiles, the frontend receives the full resume URL
   - The CandidateCard component displays the filename and provides a download button

4. **Downloading**:
   - Clicking the download button opens the resume URL in a new tab
   - Browser handles the file download based on content type
   - No embedded document viewer is used, keeping the interface clean and simple

## Usage Guide

1. **Access the Application**:
   - Frontend: `http://localhost:3000`
   - Backend API: `http://localhost:8000`
   - Django Admin: `http://localhost:8000/admin`

2. **Login**:
   - Use the credentials of the superuser you created
   - Or register a new user via the API or Django admin

3. **Navigate the Dashboard**:
   - Use the sidebar to switch between different sections
   - The Dashboard shows general information and admin access
   - Click on Jobs to manage job listings
   - Click on Candidates to manage candidate profiles

4. **Manage Jobs**:
   - Click "Create New Job" to add a job listing
   - Fill in the details (all fields are optional)
   - View all jobs as cards with status indicators
   - Edit or delete jobs using the buttons on each card
   - Click "Filter Candidates" to match candidates with this job

5. **Manage Candidates**:
   - Click "Create New Candidate" to add a candidate profile
   - Fill in the details (all fields are optional)
   - Upload resumes if needed
   - View all candidates as cards with status indicators
   - Edit or delete candidates using the buttons on each card

6. **Filter Candidates for Jobs**:
   - Click the "Filter Candidates" button on any job card
   - Select hiring statuses to filter by (e.g., L1 Scheduled, L2 Selected)
   - View candidates matching both the job title and selected statuses
   - Save filters with a name for future reference
   - Access saved filters via the "View Saved Filters" button on the Jobs page
   - Refilter to update with newly added candidates

7. **Rank Candidates**:
   - Access saved candidate filters
   - Click the "Rank Candidates" button
   - Review AI-generated scores and reasoning for each candidate
   - Use the rankings to prioritize candidates in your hiring process

## Troubleshooting

### Frontend Issues

1. **Tailwind CSS Configuration**:
   - If you encounter Tailwind CSS errors, ensure you have these files:
     - `tailwind.config.js`: Contains Tailwind configuration
     - `postcss.config.js`: Contains PostCSS configuration with Tailwind plugin
     - Updated `package.json` with correct dependencies

2. **Admin Panel Not Displaying**:
   - Check if your user has admin privileges in the backend
   - Login alert will show your admin status
   - Ensure routes are correctly set up in `App.js`
   - Check browser console for debugging messages

3. **File Upload Issues**:
   - Ensure your backend has proper MEDIA_ROOT and MEDIA_URL settings
   - Check file permissions on the server
   - Make sure FormData is correctly set up for multipart/form-data

### Backend Issues

1. **Database Connection**:
   - Ensure PostgreSQL is running
   - Verify database credentials in `settings.py`
   - Confirm database "talentHero" exists

2. **Admin Access**:
   - Use Django admin (`http://localhost:8000/admin`) to verify user permissions
   - Check if user has at least one of: is_admin, is_staff, or is_superuser set to True

3. **API Errors**:
   - Check Django server console for error messages
   - Verify request format in browser Network tab

4. **Ollama Integration Issues**:
   - Ensure Ollama is running locally on port 11434
   - Verify the cogito:3b model is installed (`ollama pull cogito:3b`)
   - Check network connections between Django and Ollama
   - For persistent errors, try restarting the Ollama service

## API Endpoints

### Authentication
- `POST /api/auth/register/`: Register a new user
- `POST /api/auth/login/`: Log in with email and password
- `POST /api/auth/logout/`: Log out and invalidate token
- `POST /api/auth/admin/create-user/`: Create a new user (admin only)
- `GET /api/auth/users/`: Get a list of all users (admin only)

### Jobs
- `GET /api/jobs/`: List all jobs
- `POST /api/jobs/`: Create a new job
- `GET /api/jobs/<id>/`: Get job details
- `PATCH /api/jobs/<id>/`: Update a job
- `DELETE /api/jobs/<id>/`: Delete a job

### Candidates
- `GET /api/candidates/`: List all candidates
- `POST /api/candidates/`: Create a new candidate
- `GET /api/candidates/<id>/`: Get candidate details
- `PATCH /api/candidates/<id>/`: Update a candidate
- `DELETE /api/candidates/<id>/`: Delete a candidate
- `POST /api/candidates/rank/`: Rank candidates against a job description
- `GET /api/candidates/transcripts/`: Get interview transcripts (filter by candidate_id)
- `POST /api/candidates/transcripts/`: Upload a new interview transcript
- `GET /api/candidates/evaluate-transcript/`: Evaluate a transcript using AI (requires transcript_id)
- `GET /api/candidates/evaluations/`: Get transcript evaluations (filter by transcript_id)
- `GET /api/candidates/download-evaluation-report/`: Download an evaluation report as a text file
- `GET /api/candidates/debug-transcripts/`: Debug endpoint for transcript system diagnostics

## Key Files

### Backend

- `backend/talent_hero/settings.py`: Main Django configuration
- `backend/authentication/models.py`: Custom User model
- `backend/authentication/views.py`: Authentication endpoints
- `backend/jobs/models.py`: Job model definition
- `backend/jobs/views.py`: Job API endpoints
- `backend/candidates/models.py`: Candidate model definition
- `backend/candidates/views.py`: Candidate API endpoints
- `backend/candidates/ranking.py`: AI-powered candidate ranking functionality

### Frontend

- `frontend/talent-hero-frontend/src/App.js`: Main application with routes
- `frontend/talent-hero-frontend/src/contexts/AuthContext.js`: Authentication state management
- `frontend/talent-hero-frontend/src/components/Dashboard.js`: Main dashboard with sidebar
- `frontend/talent-hero-frontend/src/components/JobCard.js`: Job card component
- `frontend/talent-hero-frontend/src/components/AddJobModal.js`: Modal for adding jobs
- `frontend/talent-hero-frontend/src/components/EditJobModal.js`: Modal for editing jobs
- `frontend/talent-hero-frontend/src/components/FileUpload.js`: File upload component
- `frontend/talent-hero-frontend/src/components/CandidateCard.js`: Candidate card component
- `frontend/talent-hero-frontend/src/components/AddCandidateModal.js`: Modal for adding candidates
- `frontend/talent-hero-frontend/src/components/EditCandidateModal.js`: Modal for editing candidates
- `frontend/talent-hero-frontend/src/components/SavedFiltersView.js`: View for saved candidate filters
- `frontend/talent-hero-frontend/src/components/RankCandidatesModal.js`: Modal for ranking candidates
