from rest_framework import serializers
from .models import Candidate, CandidateRanking, InterviewTranscript, TranscriptEvaluation, InterviewLevel
from roles.models import Role

class InterviewLevelSerializer(serializers.ModelSerializer):
    status_display = serializers.CharField(source='get_status_display', read_only=True)

    class Meta:
        model = InterviewLevel
        fields = ['id', 'level_name', 'level_order', 'status', 'status_display',
                  'schedule_date', 'schedule_time', 'panel_name', 'panel_comment',
                  'created_at', 'updated_at']
        read_only_fields = ['created_at', 'updated_at']

class CandidateSerializer(serializers.ModelSerializer):
    created_by_username = serializers.SerializerMethodField()
    preferred_role_display = serializers.SerializerMethodField()
    optional_roles_display = serializers.SerializerMethodField()
    hiring_status_display = serializers.SerializerMethodField()
    l1_status_display = serializers.SerializerMethodField()
    interview_levels = InterviewLevelSerializer(many=True, read_only=True)
    resume_url = serializers.SerializerMethodField()
    resume_filename = serializers.SerializerMethodField()

    # Dynamic role handling - accepts both string values and role IDs
    preferred_role_value = serializers.CharField(write_only=True, required=False, allow_blank=True)
    optional_roles_value = serializers.CharField(write_only=True, required=False, allow_blank=True)

    class Meta:
        model = Candidate
        fields = [
            'id', 'name', 'candidate_id', 'primary_email', 'mobile', 'spoc',
            'preferred_role', 'preferred_role_display', 'optional_roles', 'optional_roles_display',
            'resume', 'resume_url', 'resume_filename', 'total_experience',
            'last_job_date', 'hiring_status', 'hiring_status_display',
            'l1_status', 'l1_status_display', 'l1_schedule_date', 'l1_schedule_time', 'l1_panel_name', 'l1_panel_comment',
            'interview_levels', 'jira_tickets', 'comments',
            'created_by', 'created_by_username', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_by', 'created_at', 'updated_at']

    def get_created_by_username(self, obj):
        return obj.created_by.username if obj.created_by else None

    def get_preferred_role_display(self, obj):
        # Try new role field first, then fall back to legacy
        # Use hasattr to check if the field exists (for migration compatibility)
        if hasattr(obj, 'preferred_role_new') and obj.preferred_role_new:
            return obj.preferred_role_new.label
        return obj.get_preferred_role_display() if obj.preferred_role else None

    def get_optional_roles_display(self, obj):
        # Try new role field first, then fall back to legacy
        # Use hasattr to check if the field exists (for migration compatibility)
        if hasattr(obj, 'optional_roles_new') and obj.optional_roles_new:
            return obj.optional_roles_new.label
        return obj.get_optional_roles_display() if obj.optional_roles else None

    def get_hiring_status_display(self, obj):
        return obj.get_hiring_status_display()

    def get_l1_status_display(self, obj):
        return obj.get_l1_status_display() if obj.l1_status else None

    def get_resume_url(self, obj):
        if obj.resume:
            request = self.context.get('request')
            if request:
                return request.build_absolute_uri(obj.resume.url)
            return obj.resume.url
        return None

    def get_resume_filename(self, obj):
        return obj.resume_filename

    def validate_preferred_role(self, value):
        """Custom validation for preferred_role to allow dynamic roles"""
        if not value:
            return value

        # Get valid choices from the model
        valid_choices = [choice[0] for choice in Candidate.ROLE_CHOICES]

        # If the value is in valid choices, return it
        if value in valid_choices:
            return value

        # If not in valid choices, check if it's a reasonable role value
        # Allow snake_case values that could be valid roles
        if isinstance(value, str) and value.replace('_', '').replace('-', '').isalnum():
            # For now, allow it but log a warning
            print(f"Warning: Using non-standard role value: {value}")
            return value

        # If it's not a reasonable value, raise validation error
        raise serializers.ValidationError(f"Invalid role: {value}")

    def validate_optional_roles(self, value):
        """Custom validation for optional_roles to allow dynamic roles"""
        if not value:
            return value

        # Get valid choices from the model
        valid_choices = [choice[0] for choice in Candidate.ROLE_CHOICES]

        # If the value is in valid choices, return it
        if value in valid_choices:
            return value

        # If not in valid choices, check if it's a reasonable role value
        # Allow snake_case values that could be valid roles
        if isinstance(value, str) and value.replace('_', '').replace('-', '').isalnum():
            # For now, allow it but log a warning
            print(f"Warning: Using non-standard role value: {value}")
            return value

        # If it's not a reasonable value, raise validation error
        raise serializers.ValidationError(f"Invalid role: {value}")

    def create(self, validated_data):
        import json

        # Set the created_by field to the current user
        validated_data['created_by'] = self.context['request'].user

        # Handle dynamic role assignment (only if new fields exist)
        try:
            self._handle_role_assignment(validated_data)
        except Exception as e:
            # If role assignment fails, continue with legacy approach
            print(f"Role assignment failed, using legacy approach: {e}")
            pass

        # Extract interview levels data if present
        interview_levels_raw = self.context['request'].data.get('interview_levels', '[]')

        # Parse JSON string if it's a string
        if isinstance(interview_levels_raw, str):
            try:
                interview_levels_data = json.loads(interview_levels_raw)
            except json.JSONDecodeError:
                interview_levels_data = []
        else:
            interview_levels_data = interview_levels_raw

        # Create the candidate
        candidate = super().create(validated_data)

        # Create interview levels
        for level_data in interview_levels_data:
            # Ensure level_data is a dictionary
            if isinstance(level_data, dict):
                # Filter out empty values
                filtered_data = {k: v for k, v in level_data.items() if v not in ['', None]}
                if filtered_data:  # Only create if there's actual data
                    InterviewLevel.objects.create(
                        candidate=candidate,
                        **filtered_data
                    )

        return candidate

    def update(self, instance, validated_data):
        import json

        # Extract interview levels data if present
        interview_levels_raw = self.context['request'].data.get('interview_levels', '[]')

        # Parse JSON string if it's a string
        if isinstance(interview_levels_raw, str):
            try:
                interview_levels_data = json.loads(interview_levels_raw)
            except json.JSONDecodeError:
                interview_levels_data = []
        else:
            interview_levels_data = interview_levels_raw

        # Update the candidate
        candidate = super().update(instance, validated_data)

        # Update interview levels
        if interview_levels_data:
            # Get existing levels
            existing_levels = {level.level_name: level for level in candidate.interview_levels.all()}
            processed_levels = set()

            for level_data in interview_levels_data:
                if isinstance(level_data, dict):
                    level_name = level_data.get('level_name')
                    if level_name:
                        processed_levels.add(level_name)

                        # Filter out empty values
                        filtered_data = {k: v for k, v in level_data.items() if v not in ['', None]}

                        if level_name in existing_levels:
                            # Update existing level
                            level = existing_levels[level_name]
                            for key, value in filtered_data.items():
                                if key != 'level_name':
                                    setattr(level, key, value)
                            level.save()
                        else:
                            # Create new level
                            if filtered_data:  # Only create if there's actual data
                                InterviewLevel.objects.create(
                                    candidate=candidate,
                                    **filtered_data
                                )

            # Delete levels that are no longer present (except L1)
            for level_name, level in existing_levels.items():
                if level_name not in processed_levels and level_name != 'L1':
                    level.delete()

        return candidate

    def _handle_role_assignment(self, validated_data):
        """Handle dynamic role assignment - create roles if they don't exist"""
        request = self.context.get('request')
        user = request.user if request else None

        # Handle preferred role
        preferred_role_value = validated_data.pop('preferred_role_value', None)
        if preferred_role_value:
            role = self._get_or_create_role(preferred_role_value, user)
            if role:
                # Only set new field if it exists (migration compatibility)
                if hasattr(Candidate, 'preferred_role_new'):
                    validated_data['preferred_role_new'] = role
                validated_data['preferred_role'] = role.value  # Keep legacy field in sync

        # Handle optional role
        optional_roles_value = validated_data.pop('optional_roles_value', None)
        if optional_roles_value:
            role = self._get_or_create_role(optional_roles_value, user)
            if role:
                # Only set new field if it exists (migration compatibility)
                if hasattr(Candidate, 'optional_roles_new'):
                    validated_data['optional_roles_new'] = role
                validated_data['optional_roles'] = role.value  # Keep legacy field in sync

    def _get_or_create_role(self, role_identifier, user):
        """Get existing role or create new one"""
        if not role_identifier or not user:
            return None

        # Try to find existing role by value first
        try:
            return Role.objects.get(value=role_identifier, is_active=True)
        except Role.DoesNotExist:
            pass

        # If not found, create new role
        try:
            # Convert identifier to proper format
            role_value = role_identifier.lower().replace(' ', '_').replace('-', '_')
            role_label = role_identifier.replace('_', ' ').replace('-', ' ').title()

            role = Role.objects.create(
                value=role_value,
                label=role_label,
                created_by=user,
                is_active=True
            )
            return role
        except Exception as e:
            # Log error but don't fail the candidate creation
            print(f"Error creating role {role_identifier}: {e}")
            return None

class CandidateRankingSerializer(serializers.ModelSerializer):
    """Serializer for CandidateRanking model"""
    candidate_name = serializers.SerializerMethodField()
    job_title = serializers.SerializerMethodField()

    class Meta:
        model = CandidateRanking
        fields = ['id', 'candidate', 'job', 'score', 'reasoning',
                  'created_at', 'updated_at', 'candidate_name', 'job_title']
        read_only_fields = ['created_at', 'updated_at']

    def get_candidate_name(self, obj):
        return obj.candidate.name if obj.candidate else 'Unknown'

    def get_job_title(self, obj):
        return obj.job.title if obj.job else 'Unknown'

class InterviewTranscriptSerializer(serializers.ModelSerializer):
    level_display = serializers.CharField(source='get_level_display', read_only=True)
    has_evaluation = serializers.SerializerMethodField()

    class Meta:
        model = InterviewTranscript
        fields = ['id', 'candidate', 'level', 'level_display', 'transcript_file',
                  'uploaded_at', 'filename', 'has_evaluation']
        read_only_fields = ['uploaded_at', 'filename']

    def get_has_evaluation(self, obj):
        """Check if this transcript has an evaluation"""
        return hasattr(obj, 'evaluation')

    def validate(self, data):
        """Ensure the candidate exists and validate the file type"""
        if 'candidate' not in data:
            raise serializers.ValidationError("Candidate ID is required")

        if 'level' not in data:
            raise serializers.ValidationError("Interview level is required")

        if 'transcript_file' not in data:
            raise serializers.ValidationError("Transcript file is required")

        # Check file extension if file is provided
        if 'transcript_file' in data:
            file = data['transcript_file']
            if not file.name.lower().endswith('.pdf'):
                raise serializers.ValidationError("Only PDF files are allowed")

        return data

class TranscriptEvaluationSerializer(serializers.ModelSerializer):
    transcript_level = serializers.CharField(source='transcript.get_level_display', read_only=True)
    candidate_name = serializers.CharField(source='transcript.candidate.name', read_only=True)

    class Meta:
        model = TranscriptEvaluation
        fields = ['id', 'transcript', 'transcript_level', 'candidate_name', 'score',
                  'technical_strengths', 'improvement_areas', 'plagiarism_concerns', 'detailed_report',
                  'report_file', 'report_filename', 'created_at', 'updated_at']
        read_only_fields = ['created_at', 'updated_at', 'report_filename']
