from rest_framework import viewsets, permissions, status
from rest_framework.decorators import action
from rest_framework.response import Response
from .models import Role
from .serializers import RoleSerializer


class RoleViewSet(viewsets.ModelViewSet):
    """
    API endpoints for managing roles
    """
    queryset = Role.objects.filter(is_active=True)
    serializer_class = RoleSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        """Filter roles based on query parameters"""
        queryset = Role.objects.all()
        
        # Filter by active status
        is_active = self.request.query_params.get('is_active', None)
        if is_active is not None:
            queryset = queryset.filter(is_active=is_active.lower() == 'true')
        else:
            # By default, only show active roles
            queryset = queryset.filter(is_active=True)
        
        return queryset.order_by('label')

    @action(detail=False, methods=['post'])
    def bulk_create(self, request):
        """
        Create multiple roles at once
        Expected format: [{"value": "role1", "label": "Role 1"}, ...]
        """
        if not isinstance(request.data, list):
            return Response(
                {"error": "Expected a list of role objects"}, 
                status=status.HTTP_400_BAD_REQUEST
            )

        created_roles = []
        errors = []

        for role_data in request.data:
            serializer = self.get_serializer(data=role_data)
            if serializer.is_valid():
                role = serializer.save()
                created_roles.append(RoleSerializer(role).data)
            else:
                errors.append({
                    "data": role_data,
                    "errors": serializer.errors
                })

        return Response({
            "created": created_roles,
            "errors": errors,
            "created_count": len(created_roles),
            "error_count": len(errors)
        }, status=status.HTTP_201_CREATED if created_roles else status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['get'])
    def choices(self, request):
        """
        Return roles in a format suitable for frontend dropdowns
        """
        roles = self.get_queryset()
        choices = [{"value": role.value, "label": role.label} for role in roles]
        return Response(choices)
