#!/usr/bin/env python3
"""
Script to test InterviewLevel model and functionality
"""

import os
import sys
import json

# Add the backend directory to the Python path
backend_dir = os.path.join(os.path.dirname(__file__), 'backend')
sys.path.insert(0, backend_dir)

# Set Django settings
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'talent_hero.settings')

import django
django.setup()

from django.db import connection
from candidates.models import Candidate, InterviewLevel
from django.contrib.auth.models import User

def test_interview_level_model():
    print("=== Testing InterviewLevel Model ===")
    
    try:
        # Test if InterviewLevel table exists
        with connection.cursor() as cursor:
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='candidates_interviewlevel';")
            result = cursor.fetchone()
            if result:
                print("✓ InterviewLevel table exists")
            else:
                print("✗ InterviewLevel table does not exist")
                return False
    except Exception as e:
        print(f"✗ Error checking InterviewLevel table: {e}")
        return False
    
    try:
        # Test creating an InterviewLevel
        user, created = User.objects.get_or_create(
            username='test_user',
            defaults={'email': '<EMAIL>'}
        )
        
        candidate, created = Candidate.objects.get_or_create(
            name="Test Candidate",
            defaults={
                'candidate_id': 'TEST001',
                'created_by': user
            }
        )
        
        # Test creating interview levels
        level_data = {
            'level_name': 'L1',
            'level_order': 1,
            'status': 'scheduled',
            'schedule_date': '2024-01-01',
            'panel_name': 'Test Panel',
            'panel_comment': 'Test comment'
        }
        
        level = InterviewLevel.objects.create(
            candidate=candidate,
            **level_data
        )
        
        print(f"✓ Created InterviewLevel: {level}")
        
        # Test querying
        levels = candidate.interview_levels.all()
        print(f"✓ Candidate has {levels.count()} interview levels")
        
        # Clean up
        level.delete()
        if created:
            candidate.delete()
        
        return True
        
    except Exception as e:
        print(f"✗ Error testing InterviewLevel model: {e}")
        import traceback
        print(traceback.format_exc())
        return False

def test_json_parsing():
    print("\n=== Testing JSON Parsing ===")
    
    # Test the same JSON structure that's being sent from frontend
    test_json = '[{"level_name":"L1","level_order":1,"status":"selected","schedule_date":"2025-06-11","schedule_time":"10:23","panel_name":"Brinda Ashok","panel_comment":"Very Good"},{"level_name":"L2","level_order":2,"status":"scheduled","schedule_date":"2025-06-20","schedule_time":"10:24","panel_name":"Daniel Powtell","panel_comment":""}]'
    
    try:
        parsed_data = json.loads(test_json)
        print(f"✓ JSON parsing successful: {len(parsed_data)} levels")
        
        for i, level_data in enumerate(parsed_data):
            print(f"  Level {i+1}: {level_data}")
            
            # Test filtering empty values
            filtered_data = {k: v for k, v in level_data.items() if v not in ['', None]}
            print(f"  Filtered: {filtered_data}")
        
        return True
        
    except Exception as e:
        print(f"✗ JSON parsing failed: {e}")
        return False

def test_serializer_logic():
    print("\n=== Testing Serializer Logic ===")
    
    try:
        from candidates.serializers import CandidateSerializer
        
        # Simulate the request data
        class MockRequest:
            def __init__(self, data):
                self.data = data
                self.user = User.objects.first()
        
        mock_data = {
            'name': 'Test Candidate',
            'candidate_id': 'TEST123',
            'interview_levels': '[{"level_name":"L1","level_order":1,"status":"selected","schedule_date":"2025-06-11","schedule_time":"10:23","panel_name":"Brinda Ashok","panel_comment":"Very Good"}]'
        }
        
        mock_request = MockRequest(mock_data)
        
        # Test the serializer logic
        serializer = CandidateSerializer(context={'request': mock_request})
        
        # Test JSON parsing logic
        interview_levels_raw = mock_request.data.get('interview_levels', '[]')
        print(f"Raw data: {interview_levels_raw}")
        print(f"Type: {type(interview_levels_raw)}")
        
        if isinstance(interview_levels_raw, str):
            try:
                interview_levels_data = json.loads(interview_levels_raw)
                print(f"✓ Parsed successfully: {interview_levels_data}")
            except json.JSONDecodeError as e:
                print(f"✗ JSON decode error: {e}")
                return False
        
        return True
        
    except Exception as e:
        print(f"✗ Serializer logic test failed: {e}")
        import traceback
        print(traceback.format_exc())
        return False

if __name__ == "__main__":
    print("Testing InterviewLevel functionality...")
    
    model_ok = test_interview_level_model()
    json_ok = test_json_parsing()
    serializer_ok = test_serializer_logic()
    
    if model_ok and json_ok and serializer_ok:
        print("\n✓ All tests passed!")
    else:
        print("\n✗ Some tests failed. Check the output above.")
        
        if not model_ok:
            print("\nTo fix model issues, run:")
            print("cd backend")
            print("python manage.py migrate candidates")
