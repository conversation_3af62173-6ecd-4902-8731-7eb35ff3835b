from django.db import models
from django.utils import timezone
from django.conf import settings
import os

# Create your models here.

def job_description_upload_path(instance, filename):
    """
    Generate a path for uploading job description documents
    Stores in: media/job_descriptions/[filename]
    """
    return os.path.join(settings.JOB_DESCRIPTION_DIR, filename)

class Job(models.Model):
    STATUS_CHOICES = (
        ('active', 'Actively Recruiting'),
        ('hold', 'On Hold'),
        ('stopped', 'Stopped'),
    )
    
    # Legacy TITLE_CHOICES kept for migration compatibility
    TITLE_CHOICES = (
        ('devops', 'DevOps Engineer'),
        ('data_analyst', 'Data Analyst'),
        ('qa_testing', 'QA Testing Engineer'),
        ('java_fullstack', 'Java Full Stack Engineer'),
        ('python_developer', 'Python Developer'),
        ('servicenow', 'ServiceNow Specialist'),
        ('rpa_developer', 'RPA Developer'),
    )

    # New dynamic role field
    title_new = models.ForeignKey('roles.Role', on_delete=models.SET_NULL, blank=True, null=True,
                                 related_name='jobs', help_text="Job role/title")

    # Legacy title field - will be removed after migration
    title = models.CharField(max_length=200, choices=TITLE_CHOICES, blank=True, null=True)
    description_document = models.FileField(upload_to=job_description_upload_path, blank=True, null=True)
    positions_available = models.PositiveIntegerField(blank=True, null=True)
    hiring_team = models.CharField(max_length=200, blank=True, null=True)
    hiring_manager = models.CharField(max_length=100, blank=True, null=True)
    ta_incharge = models.CharField(max_length=100, blank=True, null=True)
    created_on = models.DateField(default=timezone.now, blank=True, null=True)
    recruitment_status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='active')
    created_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='created_jobs')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        return self.get_title_display() or "Untitled Job"
        
    @property
    def document_filename(self):
        """Return just the filename of the document, not the full path"""
        if self.description_document:
            return os.path.basename(self.description_document.name)
        return None
