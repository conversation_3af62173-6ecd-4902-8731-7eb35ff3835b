# Generated manually for InterviewLevel model

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('candidates', '0003_add_l1_fields'),
    ]

    operations = [
        migrations.CreateModel(
            name='InterviewLevel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('level_name', models.CharField(help_text='Level name (L1, L2, L3, etc.)', max_length=10)),
                ('level_order', models.IntegerField(help_text='Order of the level (1, 2, 3, etc.)')),
                ('status', models.CharField(blank=True, choices=[('scheduled', 'Scheduled'), ('attended', 'Attended'), ('hold', 'Hold'), ('rejected', 'Rejected'), ('selected', 'Selected'), ('dropoff', 'DropOff')], max_length=20, null=True)),
                ('schedule_date', models.DateField(blank=True, null=True)),
                ('schedule_time', models.TimeField(blank=True, null=True)),
                ('panel_name', models.CharField(blank=True, max_length=100, null=True)),
                ('panel_comment', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('candidate', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='interview_levels', to='candidates.candidate')),
            ],
            options={
                'ordering': ['level_order'],
            },
        ),
        migrations.AlterUniqueTogether(
            name='interviewlevel',
            unique_together={('candidate', 'level_name')},
        ),
    ]
