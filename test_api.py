#!/usr/bin/env python3
"""
Simple script to test the API endpoints and identify issues
"""

import requests
import json

# Configuration
API_BASE_URL = "http://localhost:8000/api"
USERNAME = "admin"  # Replace with your username
PASSWORD = "admin"  # Replace with your password

def get_auth_token():
    """Get authentication token"""
    try:
        response = requests.post(f"{API_BASE_URL}/auth/login/", {
            "username": USERNAME,
            "password": PASSWORD
        })
        
        if response.status_code == 200:
            data = response.json()
            print(f"✓ Authentication successful")
            return data.get('token')
        else:
            print(f"✗ Authentication failed: {response.status_code}")
            print(f"Response: {response.text}")
            return None
    except Exception as e:
        print(f"✗ Authentication error: {e}")
        return None

def test_candidates_api(token):
    """Test the candidates API endpoint"""
    print("\n--- Testing Candidates API ---")
    
    headers = {
        'Authorization': f'Token {token}',
        'Content-Type': 'application/json'
    }
    
    try:
        response = requests.get(f"{API_BASE_URL}/candidates/", headers=headers)
        
        print(f"Status Code: {response.status_code}")
        print(f"Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✓ Candidates API working")
            print(f"Number of candidates: {len(data) if isinstance(data, list) else 'Not a list'}")
            
            if isinstance(data, list) and len(data) > 0:
                print("Sample candidate fields:")
                sample = data[0]
                for key, value in sample.items():
                    print(f"  {key}: {type(value).__name__}")
            
            return True
        else:
            print(f"✗ Candidates API failed: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"✗ Candidates API error: {e}")
        return False

def test_jobs_api(token):
    """Test the jobs API endpoint"""
    print("\n--- Testing Jobs API ---")
    
    headers = {
        'Authorization': f'Token {token}',
        'Content-Type': 'application/json'
    }
    
    try:
        response = requests.get(f"{API_BASE_URL}/jobs/", headers=headers)
        
        print(f"Status Code: {response.status_code}")
        print(f"Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✓ Jobs API working")
            print(f"Number of jobs: {len(data) if isinstance(data, list) else 'Not a list'}")
            
            if isinstance(data, list) and len(data) > 0:
                print("Sample job fields:")
                sample = data[0]
                for key, value in sample.items():
                    print(f"  {key}: {type(value).__name__}")
            
            return True
        else:
            print(f"✗ Jobs API failed: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"✗ Jobs API error: {e}")
        return False

def test_create_candidate(token):
    """Test creating a candidate with new fields"""
    print("\n--- Testing Create Candidate ---")
    
    headers = {
        'Authorization': f'Token {token}',
        'Content-Type': 'application/json'
    }
    
    candidate_data = {
        "name": "Test Candidate",
        "candidate_id": "TEST001",
        "primary_skill": "Python",
        "primary_email": "<EMAIL>",
        "mobile": "+1234567890",
        "spoc": "Test SPOC",
        "preferred_role": "python_developer",
        "total_experience": "3.5",
        "hiring_status": "no_engagement",
        "jira_tickets": "JIRA-123, JIRA-456",
        "comments": "Test candidate for API testing"
    }
    
    try:
        response = requests.post(f"{API_BASE_URL}/candidates/", 
                               json=candidate_data, 
                               headers=headers)
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 201:
            data = response.json()
            print(f"✓ Candidate created successfully")
            print(f"Created candidate ID: {data.get('id')}")
            return data.get('id')
        else:
            print(f"✗ Create candidate failed: {response.status_code}")
            print(f"Response: {response.text}")
            return None
            
    except Exception as e:
        print(f"✗ Create candidate error: {e}")
        return None

def main():
    print("=== API Testing Script ===")
    
    # Get authentication token
    token = get_auth_token()
    if not token:
        print("Cannot proceed without authentication token")
        return
    
    # Test APIs
    candidates_ok = test_candidates_api(token)
    jobs_ok = test_jobs_api(token)
    
    # Test creating a candidate
    if candidates_ok:
        created_id = test_create_candidate(token)
        if created_id:
            print(f"\n✓ All tests passed! Created candidate with ID: {created_id}")
        else:
            print(f"\n✗ Create candidate test failed")
    
    print("\n=== Test Complete ===")

if __name__ == "__main__":
    main()
