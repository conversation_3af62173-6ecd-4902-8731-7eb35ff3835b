from django.core.management.base import BaseCommand
from django.db import connection
from django.contrib.auth import get_user_model

User = get_user_model()


class Command(BaseCommand):
    help = 'Check the current migration status for the dynamic roles system'

    def handle(self, *args, **options):
        cursor = connection.cursor()
        
        self.stdout.write(self.style.SUCCESS('=== Dynamic Roles Migration Status Check ===\n'))
        
        # Check if roles app tables exist
        try:
            cursor.execute("SELECT COUNT(*) FROM roles_role")
            roles_count = cursor.fetchone()[0]
            self.stdout.write(self.style.SUCCESS(f'✓ roles_role table exists with {roles_count} roles'))
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'✗ roles_role table does not exist: {e}'))
            self.stdout.write(self.style.WARNING('  → Run: python manage.py makemigrations roles'))
            self.stdout.write(self.style.WARNING('  → Run: python manage.py migrate roles'))
        
        # Check if new candidate fields exist
        try:
            cursor.execute("SELECT preferred_role_new_id FROM candidates_candidate LIMIT 1")
            self.stdout.write(self.style.SUCCESS('✓ candidates_candidate.preferred_role_new_id exists'))
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'✗ candidates_candidate.preferred_role_new_id does not exist: {e}'))
            self.stdout.write(self.style.WARNING('  → Run: python manage.py makemigrations candidates'))
            self.stdout.write(self.style.WARNING('  → Run: python manage.py migrate candidates'))
        
        # Check if new job fields exist
        try:
            cursor.execute("SELECT title_new_id FROM jobs_job LIMIT 1")
            self.stdout.write(self.style.SUCCESS('✓ jobs_job.title_new_id exists'))
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'✗ jobs_job.title_new_id does not exist: {e}'))
            self.stdout.write(self.style.WARNING('  → Run: python manage.py makemigrations jobs'))
            self.stdout.write(self.style.WARNING('  → Run: python manage.py migrate jobs'))
        
        self.stdout.write(self.style.SUCCESS('\n=== Migration Steps ==='))
        self.stdout.write('1. Run: python manage.py makemigrations roles')
        self.stdout.write('2. Run: python manage.py migrate roles')
        self.stdout.write('3. Run: python manage.py makemigrations candidates')
        self.stdout.write('4. Run: python manage.py migrate candidates')
        self.stdout.write('5. Run: python manage.py makemigrations jobs')
        self.stdout.write('6. Run: python manage.py migrate jobs')
        self.stdout.write('7. Run: python manage.py migrate_roles --dry-run  # to preview data migration')
        self.stdout.write('8. Run: python manage.py migrate_roles  # to migrate existing data')
        
        self.stdout.write(self.style.SUCCESS('\n=== Current Status ==='))
        self.stdout.write('The system is currently using legacy hardcoded roles.')
        self.stdout.write('After running the migrations above, you will have dynamic role management.')
        self.stdout.write('The bulk upload "0 candidates created" issue will be resolved after migration.')
