from rest_framework import serializers
from datetime import datetime, date
from .models import Job
from roles.models import Role

class JobSerializer(serializers.ModelSerializer):
    created_by_username = serializers.SerializerMethodField()
    recruitment_status_display = serializers.SerializerMethodField()
    title_display = serializers.SerializerMethodField()
    description_document_url = serializers.SerializerMethodField()
    description_document_filename = serializers.SerializerMethodField()

    # Dynamic role handling
    title_value = serializers.CharField(write_only=True, required=False, allow_blank=True)
    
    class Meta:
        model = Job
        fields = [
            'id', 'title', 'title_display', 'description_document', 'description_document_url', 'description_document_filename',
            'positions_available', 'hiring_team', 'hiring_manager', 'ta_incharge', 
            'created_on', 'recruitment_status', 'recruitment_status_display', 
            'created_by', 'created_by_username', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_by', 'created_at', 'updated_at']
    
    def get_created_by_username(self, obj):
        return obj.created_by.username if obj.created_by else None
        
    def get_recruitment_status_display(self, obj):
        return obj.get_recruitment_status_display()
    
    def get_title_display(self, obj):
        # Try new role field first, then fall back to legacy
        # Use hasattr to check if the field exists (for migration compatibility)
        if hasattr(obj, 'title_new') and obj.title_new:
            return obj.title_new.label
        return obj.get_title_display() if obj.title else None
    
    def get_description_document_url(self, obj):
        if obj.description_document:
            request = self.context.get('request')
            if request:
                return request.build_absolute_uri(obj.description_document.url)
            return obj.description_document.url
        return None
    
    def get_description_document_filename(self, obj):
        return obj.document_filename
    
    def create(self, validated_data):
        # Set the created_by field to the current user
        validated_data['created_by'] = self.context['request'].user

        # Handle dynamic role assignment
        self._handle_role_assignment(validated_data)

        return super().create(validated_data)

    def _handle_role_assignment(self, validated_data):
        """Handle dynamic role assignment - create roles if they don't exist"""
        request = self.context.get('request')
        user = request.user if request else None

        # Handle title role
        title_value = validated_data.pop('title_value', None)
        if title_value:
            role = self._get_or_create_role(title_value, user)
            if role:
                # Only set new field if it exists (migration compatibility)
                from .models import Job
                if hasattr(Job, 'title_new'):
                    validated_data['title_new'] = role
                validated_data['title'] = role.value  # Keep legacy field in sync

    def _get_or_create_role(self, role_identifier, user):
        """Get existing role or create new one"""
        if not role_identifier or not user:
            return None

        # Try to find existing role by value first
        try:
            return Role.objects.get(value=role_identifier, is_active=True)
        except Role.DoesNotExist:
            pass

        # If not found, create new role
        try:
            # Convert identifier to proper format
            role_value = role_identifier.lower().replace(' ', '_').replace('-', '_')
            role_label = role_identifier.replace('_', ' ').replace('-', ' ').title()

            role = Role.objects.create(
                value=role_value,
                label=role_label,
                created_by=user,
                is_active=True
            )
            return role
        except Exception as e:
            # Log error but don't fail the job creation
            print(f"Error creating role {role_identifier}: {e}")
            return None

    def to_internal_value(self, data):
        # List all DateField names here
        date_fields = ['created_on', 'start_date', 'end_date']  # Update with your actual field names
        for field in date_fields:
            if field in data and data[field]:
                if isinstance(data[field], str):
                    try:
                        # Parse the date string to a date object
                        parsed_date = datetime.fromisoformat(data[field].replace('Z', '+00:00'))
                        data[field] = parsed_date.date()
                    except (ValueError, TypeError):
                        pass
                elif isinstance(data[field], datetime):
                    # Convert datetime to date
                    data[field] = data[field].date()
        return super().to_internal_value(data)

    def to_representation(self, instance):
        ret = super().to_representation(instance)
        # List all DateField names here
        date_fields = ['created_on', 'start_date', 'end_date']  # Update with your actual field names
        for field in date_fields:
            if field in ret and ret[field] is not None:
                value = getattr(instance, field, None)
                if isinstance(value, datetime):
                    ret[field] = value.date().isoformat()
        return ret
