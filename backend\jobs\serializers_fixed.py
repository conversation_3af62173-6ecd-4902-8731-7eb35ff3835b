from rest_framework import serializers
from datetime import datetime, date
from .models import Job


class JobSerializer(serializers.ModelSerializer):
    created_by_username = serializers.SerializerMethodField()
    recruitment_status_display = serializers.SerializerMethodField()
    title_display = serializers.SerializerMethodField()
    description_document_url = serializers.SerializerMethodField()
    description_document_filename = serializers.SerializerMethodField()
    
    # Handle datetime fields properly
    created_on = serializers.DateTimeField(read_only=True)

    class Meta:
        model = Job
        fields = [
            'id', 'title', 'title_display', 'description_document', 'description_document_url', 'description_document_filename',
            'positions_available', 'hiring_team', 'hiring_manager', 'ta_incharge', 
            'created_on', 'recruitment_status', 'recruitment_status_display', 
            'created_by', 'created_by_username', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_by', 'created_at', 'updated_at']

    def get_created_by_username(self, obj):
        return obj.created_by.username if obj.created_by else None

    def get_recruitment_status_display(self, obj):
        return obj.get_recruitment_status_display()

    def get_title_display(self, obj):
        # Simple display formatting since we removed choices constraint
        if obj.title:
            # Format the title value nicely for display
            return obj.title.replace('_', ' ').title()
        return None

    def get_description_document_url(self, obj):
        if obj.description_document:
            request = self.context.get('request')
            if request:
                return request.build_absolute_uri(obj.description_document.url)
            return obj.description_document.url
        return None

    def get_description_document_filename(self, obj):
        return obj.document_filename

    def create(self, validated_data):
        # Set the created_by field to the current user
        validated_data['created_by'] = self.context['request'].user
        return super().create(validated_data)
