{"name": "talent-hero-frontend", "version": "0.1.0", "private": true, "proxy": "http://localhost:8000", "dependencies": {"@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "@headlessui/react": "^1.7.18", "@heroicons/react": "^2.1.1", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "axios": "^1.6.7", "formik": "^2.4.5", "react": "^18.2.0", "react-dom": "^18.2.0", "react-icons": "^5.5.0", "react-router-dom": "^6.22.3", "react-scripts": "5.0.1", "react-transition-group": "^4.4.5", "web-vitals": "^2.1.4", "yup": "^1.3.3"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"autoprefixer": "^10.4.17", "postcss": "^8.4.35", "tailwindcss": "^3.4.1"}}