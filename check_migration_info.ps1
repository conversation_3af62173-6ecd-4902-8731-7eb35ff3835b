# Migration Information Gathering Script for TalentHero
# Run this script from the backend directory

Write-Host "=== TalentHero Migration Information Gathering ===" -ForegroundColor Green
Write-Host ""

# Change to backend directory if not already there
if (Test-Path "manage.py") {
    Write-Host "✓ Found manage.py - we're in the right directory" -ForegroundColor Green
} elseif (Test-Path "backend\manage.py") {
    Set-Location "backend"
    Write-Host "✓ Changed to backend directory" -ForegroundColor Green
} else {
    Write-Host "✗ Cannot find manage.py. Please run this script from the project root or backend directory" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "1. CHECKING RECENT MIGRATION FILES..." -ForegroundColor Yellow
Write-Host "--- Candidates Migrations (last 10) ---"
Get-ChildItem "candidates\migrations\*.py" | Sort-Object LastWriteTime | Select-Object -Last 10 | ForEach-Object {
    Write-Host "$($_.Name) - $($_.LastWriteTime)" -ForegroundColor Cyan
}

Write-Host ""
Write-Host "--- Jobs Migrations (last 10) ---"
Get-ChildItem "jobs\migrations\*.py" | Sort-Object LastWriteTime | Select-Object -Last 10 | ForEach-Object {
    Write-Host "$($_.Name) - $($_.LastWriteTime)" -ForegroundColor Cyan
}

Write-Host ""
Write-Host "--- Roles Migrations ---"
if (Test-Path "roles\migrations") {
    Get-ChildItem "roles\migrations\*.py" | ForEach-Object {
        Write-Host "$($_.Name) - $($_.LastWriteTime)" -ForegroundColor Cyan
    }
} else {
    Write-Host "roles/migrations directory does not exist" -ForegroundColor Gray
}

Write-Host ""
Write-Host "2. CHECKING MIGRATION STATUS..." -ForegroundColor Yellow
Write-Host "--- Candidates Migration Status ---"
try {
    python manage.py showmigrations candidates
} catch {
    Write-Host "Error checking candidates migrations: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "--- Jobs Migration Status ---"
try {
    python manage.py showmigrations jobs
} catch {
    Write-Host "Error checking jobs migrations: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "--- Roles Migration Status ---"
try {
    python manage.py showmigrations roles
} catch {
    Write-Host "Roles app not found or not installed" -ForegroundColor Gray
}

Write-Host ""
Write-Host "3. SEARCHING FOR PROBLEMATIC FIELDS..." -ForegroundColor Yellow
Write-Host "--- Looking for ForeignKey references ---"

$problemFields = @("preferred_role_new", "optional_roles_new", "title_new")
$foundProblems = $false

foreach ($field in $problemFields) {
    Write-Host "Searching for '$field'..." -ForegroundColor Gray
    
    # Search in candidates migrations
    Get-ChildItem "candidates\migrations\*.py" | ForEach-Object {
        $content = Get-Content $_.FullName -Raw
        if ($content -match $field) {
            Write-Host "  FOUND in $($_.Name)" -ForegroundColor Red
            $foundProblems = $true
        }
    }
    
    # Search in jobs migrations
    Get-ChildItem "jobs\migrations\*.py" | ForEach-Object {
        $content = Get-Content $_.FullName -Raw
        if ($content -match $field) {
            Write-Host "  FOUND in $($_.Name)" -ForegroundColor Red
            $foundProblems = $true
        }
    }
}

if (-not $foundProblems) {
    Write-Host "No problematic field references found" -ForegroundColor Green
}

Write-Host ""
Write-Host "4. CHECKING CURRENT INSTALLED APPS..." -ForegroundColor Yellow
try {
    $settingsContent = Get-Content "talent_hero\settings.py" -Raw
    if ($settingsContent -match "INSTALLED_APPS\s*=\s*\[(.*?)\]" -and $Matches[1]) {
        Write-Host "Current INSTALLED_APPS includes:" -ForegroundColor Gray
        $apps = $Matches[1] -split "," | ForEach-Object { $_.Trim().Trim("'").Trim('"') } | Where-Object { $_ -and $_ -notmatch "^\s*#" }
        foreach ($app in $apps) {
            if ($app -match "(authentication|jobs|candidates|roles)") {
                Write-Host "  $app" -ForegroundColor Cyan
            }
        }
    }
} catch {
    Write-Host "Could not read settings.py" -ForegroundColor Red
}

Write-Host ""
Write-Host "5. CHECKING DATABASE SCHEMA..." -ForegroundColor Yellow
Write-Host "--- Current candidates_candidate table structure ---"
try {
    # Try to get table info using Django
    python -c "
import os, django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'talent_hero.settings')
django.setup()
from django.db import connection
cursor = connection.cursor()
cursor.execute(\"SELECT column_name, data_type FROM information_schema.columns WHERE table_name = 'candidates_candidate' AND column_name LIKE '%role%' ORDER BY column_name;\")
rows = cursor.fetchall()
if rows:
    for row in rows:
        print(f'  {row[0]} - {row[1]}')
else:
    print('  No role-related columns found or table does not exist')
"
} catch {
    Write-Host "Could not check database schema: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "=== INFORMATION GATHERING COMPLETE ===" -ForegroundColor Green
Write-Host ""
Write-Host "Please copy ALL the output above and send it to the developer." -ForegroundColor Yellow
Write-Host "This will help identify the exact migration issue and provide a solution." -ForegroundColor Yellow
Write-Host ""
Write-Host "Press any key to exit..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
