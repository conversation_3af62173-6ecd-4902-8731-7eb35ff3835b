#!/usr/bin/env python3
"""
Script to create migration for L1 fields
"""

import os
import sys

# Add the backend directory to the Python path
backend_dir = os.path.join(os.path.dirname(__file__), 'backend')
sys.path.insert(0, backend_dir)

# Set Django settings
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'talent_hero.settings')

import django
django.setup()

from django.core.management import call_command

if __name__ == "__main__":
    print("Creating migration for L1 fields...")
    try:
        call_command('makemigrations', 'candidates', verbosity=2)
        print("Migration created successfully!")
    except Exception as e:
        print(f"Error creating migration: {e}")
