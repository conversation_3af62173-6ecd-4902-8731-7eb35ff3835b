import React, { createContext, useState, useEffect, useContext } from 'react';
import axios from 'axios';

const AuthContext = createContext(null);

export const AuthProvider = ({ children }) => {
  const [currentUser, setCurrentUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    // Check if user is already logged in by looking for token in localStorage
    const token = localStorage.getItem('token');
    const storedUser = localStorage.getItem('user');

    if (token && storedUser) {
      setCurrentUser(JSON.parse(storedUser));
      axios.defaults.headers.common['Authorization'] = `Token ${token}`;
    }

    setLoading(false);
  }, []);

  const login = async (email, password) => {
    try {
      setError('');
      setLoading(true);

      const response = await axios.post('http://localhost:8000/api/auth/login/', {
        username: email,
        password
      });

      const { token, ...userData } = response.data;

      // Store token and user data
      localStorage.setItem('token', token);
      localStorage.setItem('user', JSON.stringify(userData));

      // Set auth header for future requests
      axios.defaults.headers.common['Authorization'] = `Token ${token}`;

      console.log("User data received:", userData); // Add debugging

      setCurrentUser(userData);
      return userData;
    } catch (err) {
      console.error("Login error:", err.response?.data || err.message);
      setError(err.response?.data?.non_field_errors?.[0] || 'Failed to login');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const register = async (userData) => {
    try {
      setError('');
      setLoading(true);

      const response = await axios.post('http://localhost:8000/api/auth/register/', userData);

      const { token, user } = response.data;

      // Store token and user data
      localStorage.setItem('token', token);
      localStorage.setItem('user', JSON.stringify(user));

      // Set auth header for future requests
      axios.defaults.headers.common['Authorization'] = `Token ${token}`;

      setCurrentUser(user);
      return user;
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to register');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const logout = async () => {
    try {
      await axios.post('http://localhost:8000/api/auth/logout/');
    } catch (err) {
      console.error('Logout error:', err);
    } finally {
      // Remove token and user data regardless of API call result
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      delete axios.defaults.headers.common['Authorization'];
      setCurrentUser(null);
    }
  };

  const createUser = async (userData) => {
    // Check if user has permission to create users
    if (!currentUser?.is_admin && !currentUser?.is_superuser) {
      setError('Only admins and superusers can create users');
      throw new Error('Permission denied');
    }

    // Check if user has permission to create admin users
    if (!currentUser?.is_superuser && userData.is_admin) {
      setError('Only superusers can create admin users');
      throw new Error('Permission denied');
    }

    // Check if user has permission to create superuser accounts
    if (!currentUser?.is_superuser && userData.is_superuser) {
      setError('Only superusers can create superuser accounts');
      throw new Error('Permission denied');
    }

    try {
      setLoading(true);
      const response = await axios.post('http://localhost:8000/api/auth/admin/create-user/', userData);
      return response.data;
    } catch (err) {
      setError(err.response?.data?.detail || err.response?.data?.message || 'Failed to create user');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const value = {
    currentUser,
    loading,
    error,
    login,
    logout,
    register,
    createUser,
    token: localStorage.getItem('token')
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

export const useAuth = () => {
  return useContext(AuthContext);
};

export default AuthContext;
