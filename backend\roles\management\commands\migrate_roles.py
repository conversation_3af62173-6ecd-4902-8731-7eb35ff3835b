from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from roles.models import Role
from candidates.models import Candidate
from jobs.models import Job

User = get_user_model()


class Command(BaseCommand):
    help = 'Migrate existing role data from hardcoded choices to dynamic Role model'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be migrated without making changes',
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        
        if dry_run:
            self.stdout.write(self.style.WARNING('DRY RUN MODE - No changes will be made'))
        
        # Get or create a system user for creating roles
        system_user, created = User.objects.get_or_create(
            username='system',
            defaults={
                'email': '<EMAIL>',
                'is_active': False,
                'is_staff': False,
            }
        )
        
        if created and not dry_run:
            self.stdout.write(f'Created system user: {system_user.username}')
        
        # Define the default roles from the hardcoded choices
        default_roles = [
            ('devops', 'DevOps Engineer'),
            ('data_analyst', 'Data Analyst'),
            ('qa_testing', 'QA Testing Engineer'),
            ('java_fullstack', 'Java Full Stack Engineer'),
            ('python_developer', 'Python Developer'),
            ('servicenow', 'ServiceNow Specialist'),
            ('rpa_developer', 'RPA Developer'),
        ]
        
        # Create Role objects
        created_roles = {}
        for value, label in default_roles:
            if dry_run:
                self.stdout.write(f'Would create role: {value} -> {label}')
                created_roles[value] = None
            else:
                role, created = Role.objects.get_or_create(
                    value=value,
                    defaults={
                        'label': label,
                        'created_by': system_user,
                        'is_active': True,
                    }
                )
                created_roles[value] = role
                if created:
                    self.stdout.write(f'Created role: {role.value} -> {role.label}')
                else:
                    self.stdout.write(f'Role already exists: {role.value} -> {role.label}')
        
        # Migrate Candidate data
        candidates = Candidate.objects.exclude(preferred_role__isnull=True).exclude(preferred_role='')
        candidate_count = 0
        
        for candidate in candidates:
            if candidate.preferred_role in created_roles:
                if dry_run:
                    self.stdout.write(f'Would update candidate {candidate.name}: {candidate.preferred_role}')
                else:
                    candidate.preferred_role_new = created_roles[candidate.preferred_role]
                    candidate.save()
                candidate_count += 1
        
        # Migrate optional roles for candidates
        optional_candidates = Candidate.objects.exclude(optional_roles__isnull=True).exclude(optional_roles='')
        optional_count = 0
        
        for candidate in optional_candidates:
            if candidate.optional_roles in created_roles:
                if dry_run:
                    self.stdout.write(f'Would update candidate optional role {candidate.name}: {candidate.optional_roles}')
                else:
                    candidate.optional_roles_new = created_roles[candidate.optional_roles]
                    candidate.save()
                optional_count += 1
        
        # Migrate Job data
        jobs = Job.objects.exclude(title__isnull=True).exclude(title='')
        job_count = 0
        
        for job in jobs:
            if job.title in created_roles:
                if dry_run:
                    self.stdout.write(f'Would update job: {job.title}')
                else:
                    job.title_new = created_roles[job.title]
                    job.save()
                job_count += 1
        
        # Summary
        self.stdout.write(
            self.style.SUCCESS(
                f'\nMigration {"would be" if dry_run else "completed"}:\n'
                f'- Roles: {len(default_roles)}\n'
                f'- Candidates (preferred): {candidate_count}\n'
                f'- Candidates (optional): {optional_count}\n'
                f'- Jobs: {job_count}'
            )
        )
        
        if not dry_run:
            self.stdout.write(
                self.style.WARNING(
                    '\nNext steps:\n'
                    '1. Update serializers to use new role fields\n'
                    '2. Update frontend to use new role API\n'
                    '3. Test thoroughly\n'
                    '4. Remove legacy role fields with another migration'
                )
            )
