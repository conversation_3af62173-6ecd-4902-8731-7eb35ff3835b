import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { API_URL } from '../config';

const CustomizeMenu = ({ isOpen, onClose, onUpdate, darkMode = false }) => {
  const [activeTab, setActiveTab] = useState('jobTitles');
  const [jobTitles, setJobTitles] = useState([]);
  const [hiringStatuses, setHiringStatuses] = useState([]);
  const [newJobTitle, setNewJobTitle] = useState({ value: '', label: '' });
  const [newHiringStatus, setNewHiringStatus] = useState({ value: '', label: '' });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  
  useEffect(() => {
    if (isOpen) {
      loadCustomizationOptions();
    }
  }, [isOpen]);
  
  const loadCustomizationOptions = () => {
    // Get saved customization options from localStorage or set defaults
    const savedJobTitles = JSON.parse(localStorage.getItem('customJobTitles') || '[]');
    const savedHiringStatuses = JSON.parse(localStorage.getItem('customHiringStatuses') || '[]');
    
    // If no custom job titles, load defaults
    if (savedJobTitles.length === 0) {
      setJobTitles([
        { value: 'devops', label: 'DevOps Engineer' },
        { value: 'data_analyst', label: 'Data Analyst' },
        { value: 'qa_testing', label: 'QA Testing Engineer' },
        { value: 'java_fullstack', label: 'Java Full Stack Engineer' },
        { value: 'python_developer', label: 'Python Developer' },
        { value: 'servicenow', label: 'ServiceNow Specialist' },
        { value: 'rpa_developer', label: 'RPA Developer' }
      ]);
    } else {
      setJobTitles(savedJobTitles);
    }
    
    // If no custom hiring statuses, load defaults
    if (savedHiringStatuses.length === 0) {
      setHiringStatuses([
        { value: 'no_engagement', label: 'No Engagement' },
        { value: 'l1_scheduled', label: 'L1 Scheduled' },
        { value: 'l1_attended', label: 'L1 Attended' },
        { value: 'l1_dropoff', label: 'L1 Dropoff' },
        { value: 'l1_hold', label: 'L1 Hold' },
        { value: 'l1_rejected', label: 'L1 Rejected' },
        { value: 'l1_selected', label: 'L1 Selected' },
        { value: 'l2_scheduled', label: 'L2 Scheduled' },
        { value: 'l2_attended', label: 'L2 Attended' },
        { value: 'l2_dropoff', label: 'L2 Dropoff' },
        { value: 'l2_hold', label: 'L2 Hold' },
        { value: 'l2_rejected', label: 'L2 Rejected' },
        { value: 'l2_selected', label: 'L2 Selected' },
        { value: 'l3_scheduled', label: 'L3 Scheduled' },
        { value: 'l3_attended', label: 'L3 Attended' },
        { value: 'l3_dropoff', label: 'L3 Dropoff' },
        { value: 'l3_hold', label: 'L3 Hold' },
        { value: 'l3_rejected', label: 'L3 Rejected' },
        { value: 'l3_selected', label: 'L3 Selected' }
      ]);
    } else {
      setHiringStatuses(savedHiringStatuses);
    }
  };
  
  const handleAddJobTitle = () => {
    // Validate
    if (!newJobTitle.value || !newJobTitle.label) {
      setError('Both value and label are required for job titles.');
      return;
    }
    
    // Ensure value is snake_case
    const formattedValue = newJobTitle.value.toLowerCase().replace(/ /g, '_');
    
    // Check for duplicates
    if (jobTitles.some(title => title.value === formattedValue)) {
      setError('This job title value already exists.');
      return;
    }
    
    // Add new job title
    const updatedJobTitles = [
      ...jobTitles, 
      { value: formattedValue, label: newJobTitle.label }
    ];
    
    // Update state
    setJobTitles(updatedJobTitles);
    setNewJobTitle({ value: '', label: '' });
    
    // Save to localStorage
    localStorage.setItem('customJobTitles', JSON.stringify(updatedJobTitles));
    
    // Clear error
    setError(null);
  };
  
  const handleAddHiringStatus = () => {
    // Validate
    if (!newHiringStatus.value || !newHiringStatus.label) {
      setError('Both value and label are required for hiring statuses.');
      return;
    }
    
    // Ensure value is snake_case
    const formattedValue = newHiringStatus.value.toLowerCase().replace(/ /g, '_');
    
    // Check for duplicates
    if (hiringStatuses.some(status => status.value === formattedValue)) {
      setError('This hiring status value already exists.');
      return;
    }
    
    // Add new hiring status
    const updatedHiringStatuses = [
      ...hiringStatuses, 
      { value: formattedValue, label: newHiringStatus.label }
    ];
    
    // Update state
    setHiringStatuses(updatedHiringStatuses);
    setNewHiringStatus({ value: '', label: '' });
    
    // Save to localStorage
    localStorage.setItem('customHiringStatuses', JSON.stringify(updatedHiringStatuses));
    
    // Clear error
    setError(null);
  };
  
  const handleRemoveJobTitle = (valueToRemove) => {
    const updatedJobTitles = jobTitles.filter(title => title.value !== valueToRemove);
    setJobTitles(updatedJobTitles);
    localStorage.setItem('customJobTitles', JSON.stringify(updatedJobTitles));
  };
  
  const handleRemoveHiringStatus = (valueToRemove) => {
    const updatedHiringStatuses = hiringStatuses.filter(status => status.value !== valueToRemove);
    setHiringStatuses(updatedHiringStatuses);
    localStorage.setItem('customHiringStatuses', JSON.stringify(updatedHiringStatuses));
  };
  
  const handleSaveChanges = () => {
    // Save all changes and close modal
    if (onUpdate) {
      onUpdate({ jobTitles, hiringStatuses });
    }
    onClose();
  };
  
  if (!isOpen) return null;
  
  return (
    <div className={`fixed inset-0 z-50 overflow-y-auto ${isOpen ? 'block' : 'hidden'}`}>
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div className="fixed inset-0 transition-opacity" aria-hidden="true">
          <div className={`absolute inset-0 ${darkMode ? 'bg-gray-900' : 'bg-gray-500'} opacity-75`}></div>
        </div>
        
        <div className={`inline-block align-bottom ${darkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full`}>
          <div className="px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <h2 className={`text-lg font-medium ${darkMode ? 'text-white' : 'text-gray-900'} mb-4`}>Customize Options</h2>
            
            {/* Tabs */}
            <div className="flex border-b mb-4">
              <button
                onClick={() => setActiveTab('jobTitles')}
                className={`py-2 px-4 font-medium ${
                  activeTab === 'jobTitles' 
                    ? darkMode 
                      ? 'border-b-2 border-blue-500 text-blue-400' 
                      : 'border-b-2 border-blue-500 text-blue-600'
                    : darkMode 
                      ? 'text-gray-300 hover:text-gray-100' 
                      : 'text-gray-500 hover:text-gray-700'
                }`}
              >
                Job Titles
              </button>
              <button
                onClick={() => setActiveTab('hiringStatuses')}
                className={`py-2 px-4 font-medium ${
                  activeTab === 'hiringStatuses' 
                    ? darkMode 
                      ? 'border-b-2 border-blue-500 text-blue-400' 
                      : 'border-b-2 border-blue-500 text-blue-600'
                    : darkMode 
                      ? 'text-gray-300 hover:text-gray-100' 
                      : 'text-gray-500 hover:text-gray-700'
                }`}
              >
                Hiring Statuses
              </button>
            </div>
            
            {/* Error message */}
            {error && (
              <div className="bg-red-100 text-red-700 p-3 rounded-md mb-4">
                {error}
              </div>
            )}
            
            {/* Job Titles Tab */}
            {activeTab === 'jobTitles' && (
              <div>
                <div className="flex space-x-3 mb-4">
                  <div className="flex-1">
                    <label className={`block text-sm font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'} mb-1`}>
                      Value (system identifier)
                    </label>
                    <input
                      type="text"
                      value={newJobTitle.value}
                      onChange={(e) => setNewJobTitle({ ...newJobTitle, value: e.target.value })}
                      placeholder="e.g. data_scientist"
                      className={`w-full p-2 border ${darkMode ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400' : 'border-gray-300 text-gray-800'} rounded focus:outline-none focus:ring-2 focus:ring-blue-500`}
                    />
                    <p className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-500'} mt-1`}>
                      Technical identifier (will be converted to snake_case)
                    </p>
                  </div>
                  <div className="flex-1">
                    <label className={`block text-sm font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'} mb-1`}>
                      Label (display name)
                    </label>
                    <input
                      type="text"
                      value={newJobTitle.label}
                      onChange={(e) => setNewJobTitle({ ...newJobTitle, label: e.target.value })}
                      placeholder="e.g. Data Scientist"
                      className={`w-full p-2 border ${darkMode ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400' : 'border-gray-300 text-gray-800'} rounded focus:outline-none focus:ring-2 focus:ring-blue-500`}
                    />
                    <p className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-500'} mt-1`}>
                      Human-readable name shown in dropdowns
                    </p>
                  </div>
                  <div className="self-end pb-1.5">
                    <button
                      onClick={handleAddJobTitle}
                      className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
                    >
                      Add
                    </button>
                  </div>
                </div>
                
                <h3 className={`font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'} mb-2`}>Current Job Titles</h3>
                <div className={`${darkMode ? 'bg-gray-700' : 'bg-gray-50'} p-3 rounded overflow-y-auto flex-grow max-h-[300px]`}>
                  {jobTitles.length === 0 ? (
                    <p className={`${darkMode ? 'text-gray-400' : 'text-gray-500'} text-center py-4`}>No job titles defined.</p>
                  ) : (
                    <ul className="space-y-2">
                      {jobTitles.map((title) => (
                        <li 
                          key={title.value}
                          className={`flex justify-between items-center ${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'} p-3 rounded border`}
                        >
                          <div>
                            <p className={`font-medium ${darkMode ? 'text-white' : 'text-gray-800'}`}>{title.label}</p>
                            <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>Value: {title.value}</p>
                          </div>
                          <button
                            onClick={() => handleRemoveJobTitle(title.value)}
                            className="text-red-500 hover:text-red-700"
                          >
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                            </svg>
                          </button>
                        </li>
                      ))}
                    </ul>
                  )}
                </div>
              </div>
            )}
            
            {/* Hiring Statuses Tab */}
            {activeTab === 'hiringStatuses' && (
              <div>
                <div className="flex space-x-3 mb-4">
                  <div className="flex-1">
                    <label className={`block text-sm font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'} mb-1`}>
                      Value (system identifier)
                    </label>
                    <input
                      type="text"
                      value={newHiringStatus.value}
                      onChange={(e) => setNewHiringStatus({ ...newHiringStatus, value: e.target.value })}
                      placeholder="e.g. tech_evaluation"
                      className={`w-full p-2 border ${darkMode ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400' : 'border-gray-300 text-gray-800'} rounded focus:outline-none focus:ring-2 focus:ring-blue-500`}
                    />
                    <p className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-500'} mt-1`}>
                      Technical identifier (will be converted to snake_case)
                    </p>
                  </div>
                  <div className="flex-1">
                    <label className={`block text-sm font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'} mb-1`}>
                      Label (display name)
                    </label>
                    <input
                      type="text"
                      value={newHiringStatus.label}
                      onChange={(e) => setNewHiringStatus({ ...newHiringStatus, label: e.target.value })}
                      placeholder="e.g. Technical Evaluation"
                      className={`w-full p-2 border ${darkMode ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400' : 'border-gray-300 text-gray-800'} rounded focus:outline-none focus:ring-2 focus:ring-blue-500`}
                    />
                    <p className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-500'} mt-1`}>
                      Human-readable name shown in dropdowns
                    </p>
                  </div>
                  <div className="self-end pb-1.5">
                    <button
                      onClick={handleAddHiringStatus}
                      className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
                    >
                      Add
                    </button>
                  </div>
                </div>
                
                <h3 className={`font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'} mb-2`}>Current Hiring Statuses</h3>
                <div className={`${darkMode ? 'bg-gray-700' : 'bg-gray-50'} p-3 rounded overflow-y-auto flex-grow max-h-[300px]`}>
                  {hiringStatuses.length === 0 ? (
                    <p className={`${darkMode ? 'text-gray-400' : 'text-gray-500'} text-center py-4`}>No hiring statuses defined.</p>
                  ) : (
                    <ul className="space-y-2">
                      {hiringStatuses.map((status) => (
                        <li 
                          key={status.value}
                          className={`flex justify-between items-center ${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'} p-3 rounded border`}
                        >
                          <div>
                            <p className={`font-medium ${darkMode ? 'text-white' : 'text-gray-800'}`}>{status.label}</p>
                            <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>Value: {status.value}</p>
                          </div>
                          <button
                            onClick={() => handleRemoveHiringStatus(status.value)}
                            className="text-red-500 hover:text-red-700"
                          >
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                            </svg>
                          </button>
                        </li>
                      ))}
                    </ul>
                  )}
                </div>
              </div>
            )}
          
            <div className="mt-6 flex justify-end space-x-3">
              <button
                onClick={onClose}
                className={`px-4 py-2 ${darkMode ? 'bg-gray-700 text-gray-200 hover:bg-gray-600' : 'bg-gray-200 text-gray-800 hover:bg-gray-300'} rounded`}
              >
                Cancel
              </button>
              <button
                onClick={handleSaveChanges}
                className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
              >
                Save Changes
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CustomizeMenu;
