from rest_framework import serializers
from .models import Role


class RoleSerializer(serializers.ModelSerializer):
    created_by_username = serializers.SerializerMethodField()

    class Meta:
        model = Role
        fields = ['id', 'value', 'label', 'is_active', 'created_by', 'created_by_username', 'created_at', 'updated_at']
        read_only_fields = ['created_by', 'created_at', 'updated_at']

    def get_created_by_username(self, obj):
        return obj.created_by.username if obj.created_by else None

    def create(self, validated_data):
        validated_data['created_by'] = self.context['request'].user
        return super().create(validated_data)

    def validate_value(self, value):
        """Ensure value is snake_case and unique"""
        if not value:
            raise serializers.ValidationError("Value is required")
        
        # Convert to snake_case
        snake_case_value = value.lower().replace(' ', '_').replace('-', '_')
        
        # Check for uniqueness (excluding current instance if updating)
        queryset = Role.objects.filter(value=snake_case_value)
        if self.instance:
            queryset = queryset.exclude(pk=self.instance.pk)
        
        if queryset.exists():
            raise serializers.ValidationError("A role with this value already exists")
        
        return snake_case_value
