import React, { useState, useEffect, useContext } from 'react';
import axios from 'axios';
import { FaSpinner, FaDownload, FaCheck, FaTimes, FaUpload } from 'react-icons/fa';
import FileUpload from './FileUpload';
import { ThemeContext } from '../contexts/ThemeContext';

const TranscriptEvaluationModal = ({ isOpen, onClose, candidateId, candidateName }) => {
  const { darkMode } = useContext(ThemeContext);
  const [transcripts, setTranscripts] = useState([]);
  const [evaluations, setEvaluations] = useState({});
  const [loading, setLoading] = useState(false);
  const [activeLevel, setActiveLevel] = useState(null);
  const [error, setError] = useState('');
  const [selectedFile, setSelectedFile] = useState(null);
  const [evaluationInProgress, setEvaluationInProgress] = useState(false);
  const [uploadSuccess, setUploadSuccess] = useState(false);

  const levels = [
    { id: 'L1', name: 'Level 1' },
    { id: 'L2', name: 'Level 2' },
    { id: 'L3', name: 'Level 3' },
    { id: 'L4', name: 'Level 4' },
  ];

  // Fetch existing transcripts and evaluations when modal opens
  useEffect(() => {
    if (isOpen && candidateId) {
      fetchTranscripts();
    }
  }, [isOpen, candidateId]);

  const fetchTranscripts = async () => {
    setLoading(true);
    setError('');
    try {
      console.log(`Fetching transcripts for candidate ${candidateId}`);
      const token = localStorage.getItem('token');
      console.log(`Authorization token present: ${!!token}`);
      
      const response = await axios.get(`/api/candidates/transcripts/?candidate_id=${candidateId}`, {
        headers: {
          'Authorization': `Token ${token}`,
        },
      });
      
      console.log("Transcripts response:", response.data);
      
      if (Array.isArray(response.data)) {
        setTranscripts(response.data);
        
        // Fetch evaluations for each transcript
        const evaluationsData = {};
        for (const transcript of response.data) {
          if (transcript.has_evaluation) {
            try {
              const evalResponse = await axios.get(`/api/candidates/evaluations/?transcript_id=${transcript.id}`, {
                headers: {
                  'Authorization': `Token ${token}`,
                },
              });
              
              console.log(`Evaluation for transcript ${transcript.id}:`, evalResponse.data);
              
              if (evalResponse.data.length > 0) {
                evaluationsData[transcript.id] = evalResponse.data[0];
              }
            } catch (err) {
              console.error('Error fetching evaluation:', err);
            }
          }
        }
        setEvaluations(evaluationsData);
      } else {
        console.error("Unexpected response format:", response.data);
        setError('Received invalid data format from server');
      }
    } catch (err) {
      console.error('Error fetching transcripts:', err);
      
      // More detailed error logging
      if (err.response) {
        console.error('Response status:', err.response.status);
        console.error('Response data:', err.response.data);
        
        // Provide more specific error message based on status code
        if (err.response.status === 401) {
          setError('Authentication error. Please refresh the page and log in again.');
        } else if (err.response.status === 404) {
          setError('No transcripts found for this candidate.');
        } else {
          setError(`Server error: ${err.response.data.error || err.response.data.detail || 'Unknown error'}`);
        }
      } else if (err.request) {
        console.error('No response received:', err.request);
        setError('No response received from server. Please check your connection.');
      } else {
        console.error('Error setting up request:', err.message);
        setError(`Error: ${err.message}`);
      }
    } finally {
      setLoading(false);
    }
  };

  const handleFileChange = (file) => {
    setSelectedFile(file);
    setError('');
  };

  const handleLevelSelect = (level) => {
    setActiveLevel(level);
    setError('');
    setUploadSuccess(false);
  };

  const handleUploadSubmit = async (e) => {
    e.preventDefault();
    
    if (!selectedFile) {
      setError('Please select a file');
      return;
    }
    
    // Check if it's a PDF
    if (selectedFile.type !== 'application/pdf') {
      setError('Please upload a PDF file.');
      return;
    }
    
    setLoading(true);
    setError('');
    setUploadSuccess(false);
    
    const formData = new FormData();
    formData.append('transcript_file', selectedFile);
    formData.append('candidate', candidateId);
    formData.append('level', activeLevel);
    
    try {
      const response = await axios.post(`/api/candidates/transcripts/upload/`, formData, {
        headers: {
          'Authorization': `Token ${localStorage.getItem('token')}`,
          'Content-Type': 'multipart/form-data',
        },
      });
      
      console.log('Upload response:', response.data);
      setUploadSuccess(true);
      await fetchTranscripts();
      
      // Reset form state
      setSelectedFile(null);
    } catch (err) {
      console.error('Error uploading transcript:', err);
      
      // More detailed logging
      if (err.response) {
        console.error('Response data:', err.response.data);
        console.error('Response status:', err.response.status);
        console.error('Response headers:', err.response.headers);
        
        // Extract more specific error message if available
        const errorMessage = 
          err.response.data.detail || 
          err.response.data.transcript_file || 
          err.response.data.non_field_errors ||
          err.response.data.error ||
          'Upload failed. Please try again.';
          
        setError(typeof errorMessage === 'string' ? errorMessage : JSON.stringify(errorMessage));
      } else if (err.request) {
        console.error('No response received. Request details:', err.request);
        setError('No response received from server. Please check your connection.');
      } else {
        console.error('Error setting up request:', err.message);
        setError(`Error: ${err.message}`);
      }
    } finally {
      setLoading(false);
    }
  };

  const evaluateTranscript = async (transcriptId) => {
    setEvaluationInProgress(true);
    setError('');
    
    try {
      const response = await axios.get(`/api/candidates/evaluate-transcript/?transcript_id=${transcriptId}`, {
        headers: {
          'Authorization': `Token ${localStorage.getItem('token')}`,
        },
      });

      console.log('Evaluation response:', response.data);

      // Check if response is successful
      if (response.status === 200 && response.data) {
        // Update the evaluations state with the new evaluation
        setEvaluations(prev => ({
          ...prev,
          [transcriptId]: response.data
        }));

        // Update transcripts to mark has_evaluation
        setTranscripts(prev =>
          prev.map(t =>
            t.id === transcriptId ? { ...t, has_evaluation: true } : t
          )
        );

        // Clear any previous errors and show success
        setError('');
        console.log('Transcript evaluation completed successfully!');
      } else {
        throw new Error('Invalid response from server');
      }
    } catch (err) {
      console.error('Error evaluating transcript:', err);

      // More detailed error handling
      if (err.response) {
        console.error('Response status:', err.response.status);
        console.error('Response data:', err.response.data);
        setError(`Evaluation failed: ${err.response.data.error || err.response.data.detail || 'Server error'}`);
      } else if (err.request) {
        setError('Network error: Unable to connect to server');
      } else {
        setError('Failed to evaluate transcript. Please try again.');
      }
    } finally {
      setEvaluationInProgress(false);
    }
  };

  const downloadReport = async (evaluationId) => {
    try {
      setLoading(true);
      setError('');
      const token = localStorage.getItem('token');
      
      console.log(`Initiating download for evaluation ID: ${evaluationId}`);
      
      // Use axios to get the file as a blob
      const response = await axios.get(
        `/api/candidates/download-evaluation-report/?evaluation_id=${evaluationId}`, 
        {
          headers: {
            'Authorization': `Token ${token}`,
            // Don't specify Accept header to allow content negotiation
          },
          responseType: 'blob'  // Important: this tells axios to handle the response as binary data
        }
      );
      
      console.log('Download response received:', response.status);
      
      // Create a blob URL and trigger download
      const blob = new Blob([response.data]);
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      
      // Get filename from Content-Disposition header if available, otherwise use default
      let filename = `evaluation_report_${evaluationId}.txt`;
      const contentDisposition = response.headers['content-disposition'];
      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/);
        if (filenameMatch && filenameMatch[1]) {
          filename = filenameMatch[1].replace(/['"]/g, '');
        }
      }
      
      console.log(`Downloading file as: ${filename}`);
      
      link.setAttribute('download', filename);
      document.body.appendChild(link);
      link.click();
      
      // Clean up
      window.URL.revokeObjectURL(url);
      document.body.removeChild(link);
      setLoading(false);
    } catch (err) {
      console.error('Error downloading report:', err);
      
      // More detailed error logging
      if (err.response) {
        console.error('Response status:', err.response.status);
        console.error('Response headers:', err.response.headers);
        
        // Try to read response data if possible
        if (err.response.data instanceof Blob) {
          try {
            const text = await new Response(err.response.data).text();
            console.error('Response data:', text);
          } catch (e) {
            console.error('Could not read blob data:', e);
          }
        } else {
          console.error('Response data:', err.response.data);
        }
        
        setError(`Download failed: ${err.response.status} - ${err.response.statusText || 'Server error'}`);
      } else if (err.request) {
        console.error('No response received:', err.request);
        setError('No response received from server. Please check your connection.');
      } else {
        console.error('Error setting up request:', err.message);
        setError(`Error: ${err.message}`);
      }
      
      setLoading(false);
    }
  };

  const getDebugInfo = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await axios.get(`/api/candidates/debug-transcripts/?candidate_id=${candidateId}`, {
        headers: {
          'Authorization': `Token ${token}`,
        },
      });
      
      console.log("Debug info:", response.data);
      alert('Debug info has been logged to the console.');
    } catch (err) {
      console.error('Error fetching debug info:', err);
      alert(`Error getting debug info: ${err.message}`);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-40 flex items-center justify-center z-50">
      <div className={`${darkMode ? 'bg-gray-800 text-white' : 'bg-white'} rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-y-auto`}>
        <div className={`px-6 py-4 ${darkMode ? 'border-gray-700' : 'border-gray-200'} border-b flex justify-between items-center`}>
          <h2 className={`text-xl font-bold ${darkMode ? 'text-white' : 'text-gray-800'}`}>
            {candidateName ? `Interview Transcripts: ${candidateName}` : 'Interview Transcripts'}
          </h2>
          <button
            onClick={onClose}
            className={`${darkMode ? 'text-gray-400 hover:text-gray-200' : 'text-gray-500 hover:text-gray-700'}`}
          >
            <FaTimes size={20} />
          </button>
        </div>
        
        <div className="p-6">
          {error && (
            <div className="flex justify-between items-center">
              <div className={`${darkMode ? 'text-red-400' : 'text-red-500'}`}>{error}</div>
              <button
                onClick={getDebugInfo}
                className={`px-2 py-1 ${darkMode ? 'bg-gray-700 text-gray-300' : 'bg-gray-200 text-gray-700'} text-sm rounded`}
              >
                Debug Info
              </button>
            </div>
          )}

          {uploadSuccess && (
            <div className={`mb-4 p-3 ${darkMode ? 'bg-green-900 text-green-300' : 'bg-green-100 text-green-800'} rounded`}>
              <p className="font-medium">Transcript uploaded successfully!</p>
            </div>
          )}
          
          <div className="grid grid-cols-4 gap-4 mb-6">
            {levels.map((level) => (
              <div key={level.id} className="col-span-1">
                <button
                  onClick={() => handleLevelSelect(level.id)}
                  className={`w-full p-3 rounded-lg border ${
                    activeLevel === level.id
                      ? 'bg-blue-100 border-blue-500 text-blue-800'
                      : darkMode
                        ? 'bg-gray-700 border-gray-600 text-gray-300 hover:bg-gray-600'
                        : 'bg-white hover:bg-gray-50 border-gray-300'
                  }`}
                >
                  {level.name}
                </button>
              </div>
            ))}
          </div>

          {activeLevel && (
            <div className={`mb-6 p-4 border rounded-lg ${darkMode ? 'border-gray-600 bg-gray-700' : 'border-gray-300 bg-gray-50'}`}>
              <h3 className={`text-lg font-semibold mb-3 ${darkMode ? 'text-white' : 'text-gray-800'}`}>Upload Transcript for {levels.find(l => l.id === activeLevel)?.name}</h3>
              
              <form onSubmit={handleUploadSubmit} className="space-y-4">
                <FileUpload 
                  onFileSelect={handleFileChange} 
                  label="Select PDF Transcript" 
                  acceptTypes=".pdf"
                />
                
                <div className="flex justify-end">
                  <button
                    type="button"
                    onClick={() => setActiveLevel(null)}
                    className="px-4 py-2 mr-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300"
                    disabled={loading}
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 flex items-center"
                    disabled={loading || !selectedFile}
                  >
                    {loading ? (
                      <><FaSpinner className="animate-spin mr-1" /> Uploading...</>
                    ) : (
                      <><FaUpload className="mr-1" /> Upload</>
                    )}
                  </button>
                </div>
              </form>
            </div>
          )}

          {loading && !activeLevel ? (
            <div className="text-center py-8">
              <FaSpinner className="animate-spin text-blue-500 mx-auto" size={32} />
              <p className={`mt-3 ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Loading transcripts...</p>
            </div>
          ) : (
            <div className="space-y-6">
              {transcripts.length === 0 ? (
                <div className={`text-center py-8 ${darkMode ? 'text-gray-400 border-gray-600' : 'text-gray-500 border-gray-300'} border border-dashed rounded-lg`}>
                  <p className="mb-2">No transcripts uploaded yet.</p>
                  <p>Select an interview level above to upload a transcript.</p>
                </div>
              ) : (
                transcripts.map((transcript) => {
                  const evaluation = evaluations[transcript.id];
                  
                  return (
                    <div key={transcript.id} className={`border rounded-lg overflow-hidden ${darkMode ? 'border-gray-600' : 'border-gray-300'}`}>
                      <div className={`${darkMode ? 'bg-gray-700' : 'bg-gray-100'} px-4 py-3 flex justify-between items-center`}>
                        <h3 className={`font-medium ${darkMode ? 'text-white' : 'text-gray-800'}`}>
                          {levels.find(l => l.id === transcript.level)?.name} Transcript
                        </h3>
                        <div className="flex items-center space-x-2">
                          <span className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                            Uploaded: {new Date(transcript.uploaded_at).toLocaleDateString()}
                          </span>
                          {transcript.has_evaluation && evaluation ? (
                            <button
                              onClick={() => downloadReport(evaluation.id)}
                              className="px-3 py-1 bg-gray-200 hover:bg-gray-300 rounded flex items-center text-sm"
                            >
                              <FaDownload className="mr-1" /> Report
                            </button>
                          ) : (
                            <button 
                              onClick={() => evaluateTranscript(transcript.id)}
                              className="px-3 py-1 bg-green-600 text-white hover:bg-green-700 rounded flex items-center text-sm"
                              disabled={evaluationInProgress}
                            >
                              {evaluationInProgress ? (
                                <><FaSpinner className="animate-spin mr-1" /> Evaluating...</>
                              ) : (
                                <><FaCheck className="mr-1" /> Evaluate</>
                              )}
                            </button>
                          )}
                        </div>
                      </div>
                      
                      {evaluation ? (
                        <div className={`border-t p-4 ${darkMode ? 'bg-gray-800 border-gray-600' : 'bg-gray-50 border-gray-200'}`}>
                          <div className="flex justify-between mb-3">
                            <div className="flex items-center">
                              <span className={`font-semibold mr-2 ${darkMode ? 'text-white' : 'text-gray-800'}`}>Score:</span>
                              <span className={`px-2 py-1 rounded ${
                                evaluation.score >= 7
                                  ? darkMode ? 'bg-green-900 text-green-300' : 'bg-green-100 text-green-800'
                                  : evaluation.score >= 4
                                    ? darkMode ? 'bg-yellow-900 text-yellow-300' : 'bg-yellow-100 text-yellow-800'
                                    : darkMode ? 'bg-red-900 text-red-300' : 'bg-red-100 text-red-800'
                              }`}>
                                {evaluation.score}/10
                              </span>
                            </div>
                          </div>
                          
                          <div className="space-y-3 text-sm">
                            <div>
                              <h5 className={`font-semibold ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>Technical Strengths:</h5>
                              <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'} whitespace-pre-line`}>{evaluation.technical_strengths}</p>
                            </div>

                            <div>
                              <h5 className={`font-semibold ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>Areas for Improvement:</h5>
                              <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'} whitespace-pre-line`}>{evaluation.improvement_areas}</p>
                            </div>

                            <div>
                              <h5 className={`font-semibold ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>Plagiarism Concerns:</h5>
                              <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'} whitespace-pre-line`}>{evaluation.plagiarism_concerns}</p>
                            </div>

                            <div>
                              <h5 className={`font-semibold ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>Detailed Report:</h5>
                              <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'} whitespace-pre-line`}>{
                                evaluation.detailed_report.length > 200
                                  ? evaluation.detailed_report.substring(0, 200) + '...'
                                  : evaluation.detailed_report
                              }</p>
                            </div>
                          </div>
                        </div>
                      ) : transcript.has_evaluation ? (
                        <div className="text-center py-4">
                          <FaSpinner className="animate-spin text-blue-500 mx-auto" size={24} />
                          <p className={`mt-2 ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Loading evaluation data...</p>
                        </div>
                      ) : (
                        <div className={`text-center py-4 ${darkMode ? 'text-gray-400 border-gray-600' : 'text-gray-500 border-gray-300'} border-t border-dashed`}>
                          <p>No evaluation available. Click "Evaluate" to analyze this transcript.</p>
                        </div>
                      )}
                    </div>
                  );
                })
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default TranscriptEvaluationModal;
